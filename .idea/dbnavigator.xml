<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DBNavigator.Project.DatabaseFileManager">
    <open-files />
  </component>
  <component name="DBNavigator.Project.Settings">
    <connections />
    <browser-settings>
      <general>
        <display-mode value="TABBED" />
        <navigation-history-size value="100" />
        <show-object-details value="false" />
      </general>
      <filters>
        <object-type-filter>
          <object-type name="SCHEMA" enabled="true" />
          <object-type name="USER" enabled="true" />
          <object-type name="ROLE" enabled="true" />
          <object-type name="PRIVILEGE" enabled="true" />
          <object-type name="CHARSET" enabled="true" />
          <object-type name="TABLE" enabled="true" />
          <object-type name="VIEW" enabled="true" />
          <object-type name="MATERIALIZED_VIEW" enabled="true" />
          <object-type name="NESTED_TABLE" enabled="true" />
          <object-type name="COLUMN" enabled="true" />
          <object-type name="INDEX" enabled="true" />
          <object-type name="CONSTRAINT" enabled="true" />
          <object-type name="DATASET_TRIGGER" enabled="true" />
          <object-type name="DATABASE_TRIGGER" enabled="true" />
          <object-type name="SYNONYM" enabled="true" />
          <object-type name="SEQUENCE" enabled="true" />
          <object-type name="PROCEDURE" enabled="true" />
          <object-type name="FUNCTION" enabled="true" />
          <object-type name="PACKAGE" enabled="true" />
          <object-type name="TYPE" enabled="true" />
          <object-type name="TYPE_ATTRIBUTE" enabled="true" />
          <object-type name="ARGUMENT" enabled="true" />
          <object-type name="DIMENSION" enabled="true" />
          <object-type name="CLUSTER" enabled="true" />
          <object-type name="DBLINK" enabled="true" />
        </object-type-filter>
      </filters>
      <sorting>
        <object-type name="COLUMN" sorting-type="NAME" />
        <object-type name="FUNCTION" sorting-type="NAME" />
        <object-type name="PROCEDURE" sorting-type="NAME" />
        <object-type name="ARGUMENT" sorting-type="POSITION" />
        <object-type name="TYPE ATTRIBUTE" sorting-type="POSITION" />
      </sorting>
      <default-editors>
        <object-type name="VIEW" editor-type="SELECTION" />
        <object-type name="PACKAGE" editor-type="SELECTION" />
        <object-type name="TYPE" editor-type="SELECTION" />
      </default-editors>
    </browser-settings>
    <navigation-settings>
      <lookup-filters>
        <lookup-objects>
          <object-type name="SCHEMA" enabled="true" />
          <object-type name="USER" enabled="false" />
          <object-type name="ROLE" enabled="false" />
          <object-type name="PRIVILEGE" enabled="false" />
          <object-type name="CHARSET" enabled="false" />
          <object-type name="TABLE" enabled="true" />
          <object-type name="VIEW" enabled="true" />
          <object-type name="MATERIALIZED VIEW" enabled="true" />
          <object-type name="INDEX" enabled="true" />
          <object-type name="CONSTRAINT" enabled="true" />
          <object-type name="DATASET TRIGGER" enabled="true" />
          <object-type name="DATABASE TRIGGER" enabled="true" />
          <object-type name="SYNONYM" enabled="false" />
          <object-type name="SEQUENCE" enabled="true" />
          <object-type name="PROCEDURE" enabled="true" />
          <object-type name="FUNCTION" enabled="true" />
          <object-type name="PACKAGE" enabled="true" />
          <object-type name="TYPE" enabled="true" />
          <object-type name="DIMENSION" enabled="false" />
          <object-type name="CLUSTER" enabled="false" />
          <object-type name="DBLINK" enabled="true" />
        </lookup-objects>
        <force-database-load value="false" />
        <prompt-connection-selection value="true" />
        <prompt-schema-selection value="true" />
      </lookup-filters>
    </navigation-settings>
    <dataset-grid-settings>
      <general>
        <enable-zooming value="true" />
        <enable-column-tooltip value="true" />
      </general>
      <sorting>
        <nulls-first value="true" />
        <max-sorting-columns value="4" />
      </sorting>
      <audit-columns>
        <column-names value="" />
        <visible value="true" />
        <editable value="false" />
      </audit-columns>
    </dataset-grid-settings>
    <dataset-editor-settings>
      <text-editor-popup>
        <active value="false" />
        <active-if-empty value="false" />
        <data-length-threshold value="100" />
        <popup-delay value="1000" />
      </text-editor-popup>
      <values-actions-popup>
        <show-popup-button value="true" />
        <element-count-threshold value="1000" />
        <data-length-threshold value="250" />
      </values-actions-popup>
      <general>
        <fetch-block-size value="100" />
        <fetch-timeout value="30" />
        <trim-whitespaces value="true" />
        <convert-empty-strings-to-null value="true" />
        <select-content-on-cell-edit value="true" />
        <large-value-preview-active value="true" />
      </general>
      <filters>
        <prompt-filter-dialog value="true" />
        <default-filter-type value="BASIC" />
      </filters>
      <qualified-text-editor text-length-threshold="300">
        <content-types>
          <content-type name="Text" enabled="true" />
          <content-type name="Properties" enabled="true" />
          <content-type name="XML" enabled="true" />
          <content-type name="DTD" enabled="true" />
          <content-type name="HTML" enabled="true" />
          <content-type name="XHTML" enabled="true" />
          <content-type name="Java" enabled="true" />
          <content-type name="SQL" enabled="true" />
          <content-type name="PL/SQL" enabled="true" />
          <content-type name="JSON" enabled="true" />
          <content-type name="JSON5" enabled="true" />
          <content-type name="Groovy" enabled="true" />
          <content-type name="AIDL" enabled="true" />
          <content-type name="YAML" enabled="true" />
          <content-type name="Manifest" enabled="true" />
        </content-types>
      </qualified-text-editor>
      <record-navigation>
        <navigation-target value="VIEWER" />
      </record-navigation>
    </dataset-editor-settings>
    <code-editor-settings>
      <general>
        <show-object-navigation-gutter value="false" />
        <show-spec-declaration-navigation-gutter value="true" />
        <enable-spellchecking value="true" />
        <enable-reference-spellchecking value="false" />
      </general>
      <confirmations>
        <save-changes value="false" />
        <revert-changes value="true" />
        <exit-on-changes value="ASK" />
      </confirmations>
    </code-editor-settings>
    <code-completion-settings>
      <filters>
        <basic-filter>
          <filter-element type="RESERVED_WORD" id="keyword" selected="true" />
          <filter-element type="RESERVED_WORD" id="function" selected="true" />
          <filter-element type="RESERVED_WORD" id="parameter" selected="true" />
          <filter-element type="RESERVED_WORD" id="datatype" selected="true" />
          <filter-element type="RESERVED_WORD" id="exception" selected="true" />
          <filter-element type="OBJECT" id="schema" selected="true" />
          <filter-element type="OBJECT" id="role" selected="true" />
          <filter-element type="OBJECT" id="user" selected="true" />
          <filter-element type="OBJECT" id="privilege" selected="true" />
          <user-schema>
            <filter-element type="OBJECT" id="table" selected="true" />
            <filter-element type="OBJECT" id="view" selected="true" />
            <filter-element type="OBJECT" id="materialized view" selected="true" />
            <filter-element type="OBJECT" id="index" selected="true" />
            <filter-element type="OBJECT" id="constraint" selected="true" />
            <filter-element type="OBJECT" id="trigger" selected="true" />
            <filter-element type="OBJECT" id="synonym" selected="false" />
            <filter-element type="OBJECT" id="sequence" selected="true" />
            <filter-element type="OBJECT" id="procedure" selected="true" />
            <filter-element type="OBJECT" id="function" selected="true" />
            <filter-element type="OBJECT" id="package" selected="true" />
            <filter-element type="OBJECT" id="type" selected="true" />
            <filter-element type="OBJECT" id="dimension" selected="true" />
            <filter-element type="OBJECT" id="cluster" selected="true" />
            <filter-element type="OBJECT" id="dblink" selected="true" />
          </user-schema>
          <public-schema>
            <filter-element type="OBJECT" id="table" selected="false" />
            <filter-element type="OBJECT" id="view" selected="false" />
            <filter-element type="OBJECT" id="materialized view" selected="false" />
            <filter-element type="OBJECT" id="index" selected="false" />
            <filter-element type="OBJECT" id="constraint" selected="false" />
            <filter-element type="OBJECT" id="trigger" selected="false" />
            <filter-element type="OBJECT" id="synonym" selected="false" />
            <filter-element type="OBJECT" id="sequence" selected="false" />
            <filter-element type="OBJECT" id="procedure" selected="false" />
            <filter-element type="OBJECT" id="function" selected="false" />
            <filter-element type="OBJECT" id="package" selected="false" />
            <filter-element type="OBJECT" id="type" selected="false" />
            <filter-element type="OBJECT" id="dimension" selected="false" />
            <filter-element type="OBJECT" id="cluster" selected="false" />
            <filter-element type="OBJECT" id="dblink" selected="false" />
          </public-schema>
          <any-schema>
            <filter-element type="OBJECT" id="table" selected="true" />
            <filter-element type="OBJECT" id="view" selected="true" />
            <filter-element type="OBJECT" id="materialized view" selected="true" />
            <filter-element type="OBJECT" id="index" selected="true" />
            <filter-element type="OBJECT" id="constraint" selected="true" />
            <filter-element type="OBJECT" id="trigger" selected="true" />
            <filter-element type="OBJECT" id="synonym" selected="true" />
            <filter-element type="OBJECT" id="sequence" selected="true" />
            <filter-element type="OBJECT" id="procedure" selected="true" />
            <filter-element type="OBJECT" id="function" selected="true" />
            <filter-element type="OBJECT" id="package" selected="true" />
            <filter-element type="OBJECT" id="type" selected="true" />
            <filter-element type="OBJECT" id="dimension" selected="true" />
            <filter-element type="OBJECT" id="cluster" selected="true" />
            <filter-element type="OBJECT" id="dblink" selected="true" />
          </any-schema>
        </basic-filter>
        <extended-filter>
          <filter-element type="RESERVED_WORD" id="keyword" selected="true" />
          <filter-element type="RESERVED_WORD" id="function" selected="true" />
          <filter-element type="RESERVED_WORD" id="parameter" selected="true" />
          <filter-element type="RESERVED_WORD" id="datatype" selected="true" />
          <filter-element type="RESERVED_WORD" id="exception" selected="true" />
          <filter-element type="OBJECT" id="schema" selected="true" />
          <filter-element type="OBJECT" id="user" selected="true" />
          <filter-element type="OBJECT" id="role" selected="true" />
          <filter-element type="OBJECT" id="privilege" selected="true" />
          <user-schema>
            <filter-element type="OBJECT" id="table" selected="true" />
            <filter-element type="OBJECT" id="view" selected="true" />
            <filter-element type="OBJECT" id="materialized view" selected="true" />
            <filter-element type="OBJECT" id="index" selected="true" />
            <filter-element type="OBJECT" id="constraint" selected="true" />
            <filter-element type="OBJECT" id="trigger" selected="true" />
            <filter-element type="OBJECT" id="synonym" selected="true" />
            <filter-element type="OBJECT" id="sequence" selected="true" />
            <filter-element type="OBJECT" id="procedure" selected="true" />
            <filter-element type="OBJECT" id="function" selected="true" />
            <filter-element type="OBJECT" id="package" selected="true" />
            <filter-element type="OBJECT" id="type" selected="true" />
            <filter-element type="OBJECT" id="dimension" selected="true" />
            <filter-element type="OBJECT" id="cluster" selected="true" />
            <filter-element type="OBJECT" id="dblink" selected="true" />
          </user-schema>
          <public-schema>
            <filter-element type="OBJECT" id="table" selected="true" />
            <filter-element type="OBJECT" id="view" selected="true" />
            <filter-element type="OBJECT" id="materialized view" selected="true" />
            <filter-element type="OBJECT" id="index" selected="true" />
            <filter-element type="OBJECT" id="constraint" selected="true" />
            <filter-element type="OBJECT" id="trigger" selected="true" />
            <filter-element type="OBJECT" id="synonym" selected="true" />
            <filter-element type="OBJECT" id="sequence" selected="true" />
            <filter-element type="OBJECT" id="procedure" selected="true" />
            <filter-element type="OBJECT" id="function" selected="true" />
            <filter-element type="OBJECT" id="package" selected="true" />
            <filter-element type="OBJECT" id="type" selected="true" />
            <filter-element type="OBJECT" id="dimension" selected="true" />
            <filter-element type="OBJECT" id="cluster" selected="true" />
            <filter-element type="OBJECT" id="dblink" selected="true" />
          </public-schema>
          <any-schema>
            <filter-element type="OBJECT" id="table" selected="true" />
            <filter-element type="OBJECT" id="view" selected="true" />
            <filter-element type="OBJECT" id="materialized view" selected="true" />
            <filter-element type="OBJECT" id="index" selected="true" />
            <filter-element type="OBJECT" id="constraint" selected="true" />
            <filter-element type="OBJECT" id="trigger" selected="true" />
            <filter-element type="OBJECT" id="synonym" selected="true" />
            <filter-element type="OBJECT" id="sequence" selected="true" />
            <filter-element type="OBJECT" id="procedure" selected="true" />
            <filter-element type="OBJECT" id="function" selected="true" />
            <filter-element type="OBJECT" id="package" selected="true" />
            <filter-element type="OBJECT" id="type" selected="true" />
            <filter-element type="OBJECT" id="dimension" selected="true" />
            <filter-element type="OBJECT" id="cluster" selected="true" />
            <filter-element type="OBJECT" id="dblink" selected="true" />
          </any-schema>
        </extended-filter>
      </filters>
      <sorting enabled="true">
        <sorting-element type="RESERVED_WORD" id="keyword" />
        <sorting-element type="RESERVED_WORD" id="datatype" />
        <sorting-element type="OBJECT" id="column" />
        <sorting-element type="OBJECT" id="table" />
        <sorting-element type="OBJECT" id="view" />
        <sorting-element type="OBJECT" id="materialized view" />
        <sorting-element type="OBJECT" id="index" />
        <sorting-element type="OBJECT" id="constraint" />
        <sorting-element type="OBJECT" id="trigger" />
        <sorting-element type="OBJECT" id="synonym" />
        <sorting-element type="OBJECT" id="sequence" />
        <sorting-element type="OBJECT" id="procedure" />
        <sorting-element type="OBJECT" id="function" />
        <sorting-element type="OBJECT" id="package" />
        <sorting-element type="OBJECT" id="type" />
        <sorting-element type="OBJECT" id="dimension" />
        <sorting-element type="OBJECT" id="cluster" />
        <sorting-element type="OBJECT" id="dblink" />
        <sorting-element type="OBJECT" id="schema" />
        <sorting-element type="OBJECT" id="role" />
        <sorting-element type="OBJECT" id="user" />
        <sorting-element type="RESERVED_WORD" id="function" />
        <sorting-element type="RESERVED_WORD" id="parameter" />
      </sorting>
      <format>
        <enforce-code-style-case value="true" />
      </format>
    </code-completion-settings>
    <execution-engine-settings>
      <statement-execution>
        <fetch-block-size value="100" />
        <execution-timeout value="20" />
        <debug-execution-timeout value="600" />
        <focus-result value="false" />
        <prompt-execution value="false" />
      </statement-execution>
      <script-execution>
        <command-line-interfaces />
        <execution-timeout value="300" />
      </script-execution>
      <method-execution>
        <execution-timeout value="30" />
        <debug-execution-timeout value="600" />
        <parameter-history-size value="10" />
      </method-execution>
    </execution-engine-settings>
    <operation-settings>
      <transactions>
        <uncommitted-changes>
          <on-project-close value="ASK" />
          <on-disconnect value="ASK" />
          <on-autocommit-toggle value="ASK" />
        </uncommitted-changes>
        <multiple-uncommitted-changes>
          <on-commit value="ASK" />
          <on-rollback value="ASK" />
        </multiple-uncommitted-changes>
      </transactions>
      <session-browser>
        <disconnect-session value="ASK" />
        <kill-session value="ASK" />
        <reload-on-filter-change value="false" />
      </session-browser>
      <compiler>
        <compile-type value="KEEP" />
        <compile-dependencies value="ASK" />
        <always-show-controls value="false" />
      </compiler>
    </operation-settings>
    <ddl-file-settings>
      <extensions>
        <mapping file-type-id="VIEW" extensions="vw" />
        <mapping file-type-id="TRIGGER" extensions="trg" />
        <mapping file-type-id="PROCEDURE" extensions="prc" />
        <mapping file-type-id="FUNCTION" extensions="fnc" />
        <mapping file-type-id="PACKAGE" extensions="pkg" />
        <mapping file-type-id="PACKAGE_SPEC" extensions="pks" />
        <mapping file-type-id="PACKAGE_BODY" extensions="pkb" />
        <mapping file-type-id="TYPE" extensions="tpe" />
        <mapping file-type-id="TYPE_SPEC" extensions="tps" />
        <mapping file-type-id="TYPE_BODY" extensions="tpb" />
      </extensions>
      <general>
        <lookup-ddl-files value="true" />
        <create-ddl-files value="false" />
        <synchronize-ddl-files value="true" />
        <use-qualified-names value="false" />
        <make-scripts-rerunnable value="true" />
      </general>
    </ddl-file-settings>
    <general-settings>
      <regional-settings>
        <date-format value="MEDIUM" />
        <number-format value="UNGROUPED" />
        <locale value="SYSTEM_DEFAULT" />
        <use-custom-formats value="false" />
      </regional-settings>
      <environment>
        <environment-types>
          <environment-type id="development" name="Development" description="Development environment" color="-2430209/-12296320" readonly-code="false" readonly-data="false" />
          <environment-type id="integration" name="Integration" description="Integration environment" color="-2621494/-12163514" readonly-code="true" readonly-data="false" />
          <environment-type id="production" name="Production" description="Productive environment" color="-11574/-10271420" readonly-code="true" readonly-data="true" />
          <environment-type id="other" name="Other" description="" color="-1576/-10724543" readonly-code="false" readonly-data="false" />
        </environment-types>
        <visibility-settings>
          <connection-tabs value="true" />
          <dialog-headers value="true" />
          <object-editor-tabs value="true" />
          <script-editor-tabs value="false" />
          <execution-result-tabs value="true" />
        </visibility-settings>
      </environment>
    </general-settings>
  </component>
</project>