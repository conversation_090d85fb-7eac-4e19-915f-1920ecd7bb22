/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool.api;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.IBinder;
import android.os.Process;
import android.os.RemoteException;
import android.os.UserHandle;
import android.util.Log;

import com.thundercomm.configtool.api.constant.ConstantCode;

/**
 * ConfigTool Service Manager
 */
public class ConfigToolServiceManager {
    private static final String TAG = "ConfigToolServiceManager";

    private IConfigToolServiceManager mService;
    private IConfigToolServiceConnectCallback mConnectCallback;
    private Context mContext;
    private static final int INT_100 = 100;

    // 状态跟踪变量
    private volatile boolean mReceiverRegistered = false;
    private volatile boolean mServiceBound = false;

    // Configuration change broadcast receiver
    private final BroadcastReceiver mConfigChangedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && ConstantCode.CONFIG_CHANGED_ACTION.equals(intent.getAction())) {
                String modifiedFields = intent.getStringExtra("modified_fields");
                String addedFields = intent.getStringExtra("added_fields");
                long timestamp = intent.getLongExtra("timestamp", 0);

                if (mConnectCallback != null) {
                    mConnectCallback.onConfigChanged(modifiedFields, addedFields, timestamp);
                }
            }
        }
    };

    private static volatile ConfigToolServiceManager sInstance;

    /**
     * Get ConfigToolServiceManager instance
     *
     * @return ConfigToolServiceManager
     */
    public static ConfigToolServiceManager getInstance() {
        if (sInstance == null) {
            synchronized (ConfigToolServiceManager.class) {
                if (sInstance == null) {
                    sInstance = new ConfigToolServiceManager();
                }
            }
        }
        return sInstance;
    }

    private ConfigToolServiceManager() {
    }

    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
            mService = IConfigToolServiceManager.Stub.asInterface(iBinder);
            mServiceBound = true;
            if (mConnectCallback != null) {
                mConnectCallback.onServiceConnected();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
            mService = null;
            mServiceBound = false;
            if (mConnectCallback != null) {
                mConnectCallback.onServiceDisconnected();
            }
        }
    };

    /**
     * Set service connection callback
     *
     * @param callback Callback interface
     */
    public void setServiceConnectCallback(IConfigToolServiceConnectCallback callback) {
        mConnectCallback = callback;
        // Only notify current state if service is already connected
        // Don't trigger onServiceDisconnected immediately when setting callback
        if (mConnectCallback != null && mService != null) {
            mConnectCallback.onServiceConnected();
        }
    }

    /**
     * Bind ConfigTool service
     startService
     *
     * @param context Context
     */
    public void bindService(Context context) {
        mContext = context.getApplicationContext();

        // Register broadcast receiver (only if not already registered)
        if (!mReceiverRegistered) {
            try {
                IntentFilter filter = new IntentFilter();
                filter.addAction(ConstantCode.CONFIG_CHANGED_ACTION);
                mContext.registerReceiver(mConfigChangedReceiver, filter);
                mReceiverRegistered = true;
                Log.d(TAG, "Broadcast receiver registered successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to register broadcast receiver", e);
            }
        } else {
            Log.d(TAG, "Broadcast receiver already registered, skipping");
        }

        try {
            Log.d(TAG, "Starting ConfigTool service to ensure independent lifecycle...");
            Intent startIntent = new Intent();
            ComponentName component = new ComponentName("com.thundercomm.configtool",
                    "com.thundercomm.configtool.ConfigToolService");
            startIntent.setComponent(component);
            startIntent.setAction("com.thundercomm.configtool.ConfigToolService"); // Use existing Action

            ComponentName startResult = mContext.startService(startIntent);
            if (startResult != null) {
                Log.i(TAG, "ConfigTool service started successfully: " + startResult);
            } else {
                Log.e(TAG, "Failed to start ConfigTool service - startService returned null");
            }

            // Brief delay to give service time to start
            try {
                Thread.sleep(INT_100);
            } catch (InterruptedException es) {
                Thread.currentThread().interrupt();
            }

            // Bind service
            Log.d(TAG, "Binding to ConfigTool service...");
            Intent bindIntent = new Intent();
            bindIntent.setComponent(component);

            boolean bindResult;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                Log.d(TAG, "Using bindServiceAsUser for Android R+");
                bindResult = mContext.bindServiceAsUser(bindIntent, mServiceConnection,
                        Context.BIND_AUTO_CREATE, Process.myUserHandle());
            } else {
                Log.d(TAG, "Using bindService for Android < R");
                bindResult = mContext.bindService(bindIntent, mServiceConnection, Context.BIND_AUTO_CREATE);
            }

            Log.i(TAG, "Service bind result: " + bindResult);
            if (bindResult) {
                Log.i(TAG, "ConfigTool service binding initiated successfully");
            } else {
                Log.e(TAG, "Failed to bind ConfigTool service - bindService returned false");
            }

        } catch (SecurityException ex) {
            Log.e(TAG, "Security exception while starting/binding service", ex);
        } catch (Exception ex) {
            Log.e(TAG, "Unexpected exception while starting/binding service", ex);
        }
    }

    /**
     * Unbind ConfigTool service
     *
     * @param context Context (可以为null，将使用保存的mContext)
     */
    public void unbindService(Context context) {
        Context contextToUse = (context != null) ? context : mContext;
        if (contextToUse == null) {
            Log.w(TAG, "No context available for unbinding service");
            return;
        }

        // Unregister broadcast receiver (only if registered)
        if (mReceiverRegistered) {
            try {
                contextToUse.unregisterReceiver(mConfigChangedReceiver);
                mReceiverRegistered = false;
                Log.d(TAG, "Broadcast receiver unregistered successfully");
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "Broadcast receiver was not registered: " + e.getMessage());
                mReceiverRegistered = false; // 重置状态
            } catch (Exception e) {
                Log.e(TAG, "Unexpected error while unregistering receiver", e);
                mReceiverRegistered = false; // 重置状态
            }
        } else {
            Log.d(TAG, "Broadcast receiver not registered, skipping unregister");
        }

        // Unbind service (only if bound)
        if (mServiceBound) {
            try {
                contextToUse.unbindService(mServiceConnection);
                mServiceBound = false;
                mService = null;
                Log.d(TAG, "Service unbound successfully");
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "Service was not bound: " + e.getMessage());
                mServiceBound = false; // 重置状态
                mService = null;
            } catch (Exception e) {
                Log.e(TAG, "Unexpected error while unbinding service", e);
                mServiceBound = false; // 重置状态
                mService = null;
            }
        } else {
            Log.d(TAG, "Service not bound, skipping unbind");
        }
    }

    /**
     * Unbind ConfigTool service (使用保存的Context)
     */
    public void unbindService() {
        unbindService(null);
    }

    /**
     * Get service connection status
     *
     * @return Returns true if service is disconnected, otherwise returns false
     */
    public boolean isServiceDisconnected() {
        return mService == null;
    }

    /**
     * Save configuration data
     *
     * @param jsonConfig Configuration data in JSON format
     * @return Returns true if saved successfully, otherwise returns false
     */
    public boolean saveConfig(String jsonConfig) {
        try {
            return mService.saveConfig(jsonConfig);
        } catch (RemoteException e) {
            Log.e(TAG, "Remote exception while saving config", e);
        } catch (NullPointerException e) {
            Log.e(TAG, "Service is not connected", e);
        } catch (Exception e) {
            Log.e(TAG, "Failed to save config", e);
        }
        return false;
    }

    /**
     * Get configuration value by key
     *
     * @param key Configuration key
     * @return Returns the JSON data corresponding to the key, or null if it does not exist or an error occurs
     */
    public String getConfigValue(String key) {
        try {
            return mService.getConfigValue(key);
        } catch (RemoteException e) {
            Log.e(TAG, "Remote exception while getting config value for key: " + key, e);
        } catch (NullPointerException e) {
            Log.e(TAG, "Service is not connected", e);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get config value for key: " + key, e);
        }
        return null;
    }
}
