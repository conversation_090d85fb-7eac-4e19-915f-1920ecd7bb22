/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package com.thundercomm.configtool.api;
/**
 * Configuration Tool Service Manager Interface
 */
public interface IConfigToolServiceManager extends android.os.IInterface
{
  /** Default implementation for IConfigToolServiceManager. */
  public static class Default implements com.thundercomm.configtool.api.IConfigToolServiceManager
  {
    /**
         * Save configuration data
         *
         * @param jsonConfig Configuration data in JSON format
         * @return Returns true if saved successfully, otherwise returns false
         */
    @Override public boolean saveConfig(java.lang.String jsonConfig) throws android.os.RemoteException
    {
      return false;
    }
    /**
         * Get configuration value by key
         *
         * @param key Configuration key
         * @return Returns the JSON data corresponding to the key, or null if it does not exist or an error occurs
         */
    @Override public java.lang.String getConfigValue(java.lang.String key) throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements com.thundercomm.configtool.api.IConfigToolServiceManager
  {
    private static final java.lang.String DESCRIPTOR = "com.thundercomm.configtool.api.IConfigToolServiceManager";
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an com.thundercomm.configtool.api.IConfigToolServiceManager interface,
     * generating a proxy if needed.
     */
    public static com.thundercomm.configtool.api.IConfigToolServiceManager asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof com.thundercomm.configtool.api.IConfigToolServiceManager))) {
        return ((com.thundercomm.configtool.api.IConfigToolServiceManager)iin);
      }
      return new com.thundercomm.configtool.api.IConfigToolServiceManager.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
        case TRANSACTION_saveConfig:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          boolean _result = this.saveConfig(_arg0);
          reply.writeNoException();
          reply.writeInt(((_result)?(1):(0)));
          return true;
        }
        case TRANSACTION_getConfigValue:
        {
          data.enforceInterface(descriptor);
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _result = this.getConfigValue(_arg0);
          reply.writeNoException();
          reply.writeString(_result);
          return true;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
    }
    private static class Proxy implements com.thundercomm.configtool.api.IConfigToolServiceManager
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      /**
           * Save configuration data
           *
           * @param jsonConfig Configuration data in JSON format
           * @return Returns true if saved successfully, otherwise returns false
           */
      @Override public boolean saveConfig(java.lang.String jsonConfig) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        boolean _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(jsonConfig);
          boolean _status = mRemote.transact(Stub.TRANSACTION_saveConfig, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().saveConfig(jsonConfig);
          }
          _reply.readException();
          _result = (0!=_reply.readInt());
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      /**
           * Get configuration value by key
           *
           * @param key Configuration key
           * @return Returns the JSON data corresponding to the key, or null if it does not exist or an error occurs
           */
      @Override public java.lang.String getConfigValue(java.lang.String key) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(key);
          boolean _status = mRemote.transact(Stub.TRANSACTION_getConfigValue, _data, _reply, 0);
          if (!_status && getDefaultImpl() != null) {
            return getDefaultImpl().getConfigValue(key);
          }
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      public static com.thundercomm.configtool.api.IConfigToolServiceManager sDefaultImpl;
    }
    static final int TRANSACTION_saveConfig = (android.os.IBinder.FIRST_CALL_TRANSACTION + 0);
    static final int TRANSACTION_getConfigValue = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    public static boolean setDefaultImpl(com.thundercomm.configtool.api.IConfigToolServiceManager impl) {
      // Only one user of this interface can use this function
      // at a time. This is a heuristic to detect if two different
      // users in the same process use this function.
      if (Stub.Proxy.sDefaultImpl != null) {
        throw new IllegalStateException("setDefaultImpl() called twice");
      }
      if (impl != null) {
        Stub.Proxy.sDefaultImpl = impl;
        return true;
      }
      return false;
    }
    public static com.thundercomm.configtool.api.IConfigToolServiceManager getDefaultImpl() {
      return Stub.Proxy.sDefaultImpl;
    }
  }
  /**
       * Save configuration data
       *
       * @param jsonConfig Configuration data in JSON format
       * @return Returns true if saved successfully, otherwise returns false
       */
  public boolean saveConfig(java.lang.String jsonConfig) throws android.os.RemoteException;
  /**
       * Get configuration value by key
       *
       * @param key Configuration key
       * @return Returns the JSON data corresponding to the key, or null if it does not exist or an error occurs
       */
  public java.lang.String getConfigValue(java.lang.String key) throws android.os.RemoteException;
}
