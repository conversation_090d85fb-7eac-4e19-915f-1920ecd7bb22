<libraries>
  <library
      name="com.google.android.material:material:1.9.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/058cfc4da2ae93637a16a1362856d4c0/transformed/material-1.9.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.9.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/058cfc4da2ae93637a16a1362856d4c0/transformed/material-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/407d1b6f537eac04008ca91abbdc9327/transformed/constraintlayout-2.0.1/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/407d1b6f537eac04008ca91abbdc9327/transformed/constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/452736d1d908cf94baf2f23bc48e1513/transformed/appcompat-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/452736d1d908cf94baf2f23bc48e1513/transformed/appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/55b1b30c2cd642dff4825e542e10bc2d/transformed/viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/55b1b30c2cd642dff4825e542e10bc2d/transformed/viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/293879b0575788d8256f25540990d4b4/transformed/fragment-1.3.6/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="/home/<USER>/.gradle/caches/transforms-3/293879b0575788d8256f25540990d4b4/transformed/fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.6.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/a83a0f7737c4240db227f182fd3c5b7e/transformed/activity-1.6.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.6.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/a83a0f7737c4240db227f182fd3c5b7e/transformed/activity-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/12d7cad622d5e92a50dacc8834f619a0/transformed/appcompat-resources-1.6.1/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/12d7cad622d5e92a50dacc8834f619a0/transformed/appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/e356bb9200c3a079164d966b8661b66d/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/e356bb9200c3a079164d966b8661b66d/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/fb07d92104a2da20f42a374c156a35e0/transformed/coordinatorlayout-1.1.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/fb07d92104a2da20f42a374c156a35e0/transformed/coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/fa5778dcaf7dee024c067cd42bcd41af/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/fa5778dcaf7dee024c067cd42bcd41af/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/3f1089eaa24908819cb7fdfe19dac943/transformed/recyclerview-1.1.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/3f1089eaa24908819cb7fdfe19dac943/transformed/recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/9ae651bc0be4ddc77ce8cb6ad4e65e60/transformed/transition-1.2.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/9ae651bc0be4ddc77ce8cb6ad4e65e60/transformed/transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/6277cc59d32305b71293ef6b60270221/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/6277cc59d32305b71293ef6b60270221/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/7a926a9055ee9a9de31024875f377335/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/7a926a9055ee9a9de31024875f377335/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/510c322b9316efefe40ea99a08f955cc/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/510c322b9316efefe40ea99a08f955cc/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/2d4d69952ecddcfdb859483862a98463/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/2d4d69952ecddcfdb859483862a98463/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/c842db0bcc34078e2d2181da821616b6/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/c842db0bcc34078e2d2181da821616b6/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/927f8d31e48e1c4bb5619676eeee3618/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/927f8d31e48e1c4bb5619676eeee3618/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/8b2adfe6b37326b3a9cb5ce21074671a/transformed/lifecycle-viewmodel-savedstate-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/8b2adfe6b37326b3a9cb5ce21074671a/transformed/lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/39446842ce32bb429bcb1c4b61904c2a/transformed/core-ktx-1.9.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/39446842ce32bb429bcb1c4b61904c2a/transformed/core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0/jars/classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/6690daf909aed306e83f580bdb482290/transformed/core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/a4ba8268d762cd005cd00d9b0ad0e5d5/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/a4ba8268d762cd005cd00d9b0ad0e5d5/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/723e69cc8fd716adf353fea517505872/transformed/savedstate-1.2.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/723e69cc8fd716adf353fea517505872/transformed/savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/47e93d947b3d3b5792658216ef936168/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/47e93d947b3d3b5792658216ef936168/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/05c84dba33780c7e01077a16068d7f3a/transformed/lifecycle-runtime-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/05c84dba33780c7e01077a16068d7f3a/transformed/lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/30f39603487191cd39c3b1483fe54a0e/transformed/lifecycle-viewmodel-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/30f39603487191cd39c3b1483fe54a0e/transformed/lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/4d6ce0c5e230f69aab47d5092184037f/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/4d6ce0c5e230f69aab47d5092184037f/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/a5eb7c988f8706fd33e63d3c3a0967b0/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/a5eb7c988f8706fd33e63d3c3a0967b0/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/bcde0085e1859e68ac3cf05787644241/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/bcde0085e1859e68ac3cf05787644241/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/ac985579ba2d485d7e72974c13f50802/transformed/lifecycle-livedata-core-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/ac985579ba2d485d7e72974c13f50802/transformed/lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.5.1/1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12/lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/0bbb0c867ea7c039c7c398add3bb85dc/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/0bbb0c867ea7c039c7c398add3bb85dc/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/d63eb06babc60a06a5e3fedde949e4d7/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/d63eb06babc60a06a5e3fedde949e4d7/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/6489229e19add160c967a15ede008f79/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/6489229e19add160c967a15ede008f79/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/7a591bab6a2e1c2f6e8d01bf2e66b902/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/7a591bab6a2e1c2f6e8d01bf2e66b902/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.3.0/21f49f5f9b85fc49de712539f79123119740595/annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/74bb2d5472718acc6fe2adc4e6f0c5ce/transformed/annotation-experimental-1.3.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/74bb2d5472718acc6fe2adc4e6f0c5ce/transformed/annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.6.1/97fd74ccf54a863d221956ffcd21835e168e2aaa/kotlinx-coroutines-core-jvm-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.6.1/4e61fcdcc508cbaa37c4a284a50205d7c7767e37/kotlinx-coroutines-android-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.6.0/baf82c475e9372c25407f3d132439e4aa803b8b8/kotlin-stdlib-jdk8-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.6.0/da6bdc87391322974a43ccc00a25536ae74dad51/kotlin-stdlib-jdk7-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.7.10/d2abf9e77736acc4450dc4a3f707fa2c10f5099d/kotlin-stdlib-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.7.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.7.10/bac80c520d0a9e3f3673bc2658c6ed02ef45a76a/kotlin-stdlib-common-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.1/30988fe2d77f3fe3bf7551bb8a8b795fad7e7226/constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/e17e41a3b28fac8146f5c5c54182168b/transformed/emoji2-views-helper-1.2.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/e17e41a3b28fac8146f5c5c54182168b/transformed/emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/jars/classes.jar:/home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/135ca283e7cf3d338f7d6eff1b42490b/transformed/emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.4.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/db10c956bc7bde65dde29329887f7884/transformed/lifecycle-process-2.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/a89fbd729976d3997fa491032185eada/transformed/startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/home/<USER>/.gradle/caches/transforms-3/a89fbd729976d3997fa491032185eada/transformed/startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/transforms-3/938bc7cf3bcf678104410544166cfac3/transformed/tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/home/<USER>/.gradle/caches/transforms-3/938bc7cf3bcf678104410544166cfac3/transformed/tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.0.0/c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1/concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.15.0/38c8485a652f808c8c149150da4e5c2b0bd17f9a/error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
