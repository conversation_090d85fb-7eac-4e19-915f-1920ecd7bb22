<lint-module
    format="1"
    dir="/media/ts/sdb1/work/1-crown/3-crown_3.2/1-vendor/LINUX/android/vendor/thundercomm/apps/ConfigTool/configtoolinterface"
    name=":configtoolinterface"
    type="LIBRARY"
    maven="ConfigTool:configtoolinterface:"
    gradle="7.3.1"
    buildFolder="build"
    bootClassPath="/opt/sdk/platforms/android-33/android.jar:/opt/sdk/build-tools/30.0.3/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-33"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
