plugins {
    id 'com.android.application'
}

android {
    namespace 'com.thundercomm.configtool'
    compileSdk 33

    defaultConfig {
        applicationId "com.thundercomm.configtool"
        minSdk 30
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // Add support for AIDL
    sourceSets {
        main {
            aidl.srcDirs = ['src/main/aidl']
        }
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation files('libs/gson-2.10.1.jar')
    implementation files('libs/fileencrypt.api.jar')
    implementation files('libs/vendor.thundercomm.eventbroker-V1-java.jar')
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.android.material:material:1.9.0'
    implementation files('libs/vendor.thundercomm.eventbroker-V1-java.jar')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation project(':configtoolinterface')
}