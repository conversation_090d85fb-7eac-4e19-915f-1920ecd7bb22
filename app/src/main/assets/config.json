{"configVersion": "V001", "eventRectime": {"beforeEvent": 10, "afterEvent": 10}, "recMode": "high_q", "recCam": 10, "movieEnctyption": true, "inpactDetection": false, "accThreshold": 1.11, "audioPlay": false, "audioRec": false, "parkingRec": false, "lcdUse": true, "voltageThreshold": 2.22, "logging": true, "agps": true, "sim": "usim", "appInfo": [{"packageName": "com.thundercomm.testapp", "permission": "android.permission.ACCESS_COARSE_LOCATION,android.permission.ACCESS_FINE_LOCATION,android.permission.CAMERA,android.permission.RECORD_AUDIO,android.permission.READ_EXTERNAL_STORAGE,android.permission.WRITE_EXTERNAL_STORAGE,android.permission.MA<PERSON><PERSON>_EXTERNAL_STORAGE,android.permission.BLUETOOTH_CONNECT,android.permission.BLUETOOTH_SCAN,android.permission.POST_NOTIFICATIONS,android.permission.ACCESS_BACKGROUND_LOCATION", "autoBoot": true}], "apnInfo": [{"nwType": "open", "apnName": "xxxx", "userName": "xxx", "password": "xxx"}], "fotaTargetInfo": [{"fotaNo": "2", "targetName": "CS3", "fotaType": "yosemite", "fotaServer": "1x", "userAgent": "SSOLMS/1.0(CS3;Unit;000[1.0])"}, {"fotaNo": "21", "targetName": "com.ssol.titanApp", "fotaType": "apk", "fotaServer": "1", "userAgent": "SSOLMS/1.0(CFT5;CFT500;000:[1.0])"}, {"fotaNo": "90", "targetName": "CFT500", "fotaType": "firmware", "fotaServer": "1", "userAgent": "SSOLMS/1.0(CFT5;CFT500;000:[1.0])"}, {"fotaNo": "91", "targetName": "config", "fotaType": "config", "fotaServer": "1", "userAgent": "SSOLMS/1.0(CFT5;CFT500;000:[1.0])"}], "fotaServer": [{"no": "0", "nwType": "closed", "url": "http://ms6.ms.seiko-sol.co.jp/"}, {"no": "1", "nwType": "open", "url": "http://ms6.ms.seiko-sol.co.jp/"}], "rebootTarget": [{"no": "0", "target": "drv<PERSON><PERSON>"}, {"no": "2", "target": "com.thundercomm.testapp"}, {"no": "3", "target": "com.ssol.titanApp"}]}