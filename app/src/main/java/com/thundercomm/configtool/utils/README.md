# ConfigTool Service Functionality Description

## Core Features
ConfigTool service provides encrypted storage and decrypted reading of configuration files, ensuring configuration data is secure and easily accessible.

## Main Processes

### Configuration Loading Process
1. After service startup, it first checks the internal storage encrypted configuration file: `/data/user/0/com.thundercomm.configtool/files/config.bin`
2. If no encrypted configuration in internal storage, it tries to get a backup from external storage: `/storage/emulated/0/ext_sdcard/Config/config.bin`
3. If no backup in external storage, it tries to read the internal plaintext configuration: `/data/user/0/com.thundercomm.configtool/files/config.json`
4. If plaintext configuration is read, it encrypts and saves it to generate the encrypted file, then deletes the plaintext file

### AIDL Interface Specifications
- AIDL methods do not trigger any service loading logic
- If there are parameters, return the parameters; if no parameters, return error messages
- After saveConfig succeeds, an encrypted backup must be created in external storage

### Data Operation Principles
- Configuration file loading is executed only once, not repeatedly
- If no data is loaded, no repeated attempts are made
- Whenever saveConfig is called, encrypted files should be saved both internally and externally

## Key Paths
- Internal encrypted configuration file: `/data/user/0/com.thundercomm.configtool/files/config.bin`
- External encrypted backup file: `/storage/emulated/0/ext_sdcard/Config/config.bin`
- External JSON configuration file: `/storage/emulated/0/ext_sdcard/Config/config.json`
- Internal plaintext configuration file: `/data/user/0/com.thundercomm.configtool/files/config.json`

## Development Principles
- Develop around the core loading process to ensure service stability
- Loading failures should have reasonable error prompts and handling mechanisms
- Maintain the singleton mode of the service, avoid repeated instantiation
- Avoid adding additional loading logic in AIDL methods 