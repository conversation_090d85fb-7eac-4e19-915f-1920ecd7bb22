/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool.utils;

import android.util.Log;

/**
 * Logger Utility Class
 * Provides unified logging interface
 */
public class Logger {
    private static final String TAG = "ConfigTool";

    private static final boolean DEBUG = true; //TODO User closure

     // Number of bytes per group in hex dump
    private static final int HEX_GROUP_SIZE = 16;

     // Callback for UI logging
    private static LogCallback sLogCallback;

    // Log level control

    private Logger() {
        // Private constructor to prevent instantiation
    }

    /**
     * Log debug level message.
     *
     * @param tag Tag
     * @param msg Log message
     */
    public static void d(String tag, String msg) {
        if (DEBUG) {
            Log.d(TAG + "_" + tag, msg);
        }
    }

    /**
     * Log info level message.
     *
     * @param tag Tag
     * @param msg Log message
     */
    public static void i(String tag, String msg) {
        if (DEBUG) {
            Log.i(TAG + "_" + tag, msg);
        }
    }

    /**
     * Log warning level message.
     *
     * @param tag Tag
     * @param msg Log message
     */
    public static void w(String tag, String msg) {
        if (DEBUG) {
            Log.w(TAG + "_" + tag, msg);
        }
    }

    /**
     * Log warning level message with exception.
     *
     * @param tag Tag
     * @param msg Log message
     * @param tr Exception
     */
    public static void w(String tag, String msg, Throwable tr) {
        if (DEBUG) {
            Log.w(TAG + "_" + tag, msg, tr);
        }
    }

    /**
     * Log error level message.
     *
     * @param tag Tag
     * @param msg Log message
     */
    public static void e(String tag, String msg) {
        if (DEBUG) {
            Log.e(TAG + "_" + tag, msg);
        }
    }

    /**
     * Log error level message with exception.
     *
     * @param tag Tag
     * @param msg Log message
     * @param tr Exception
     */
    public static void e(String tag, String msg, Throwable tr) {
        if (DEBUG) {
            Log.e(TAG + "_" + tag, msg, tr);
        }
    }

    /**
     * Log verbose level message.
     *
     * @param tag Tag
     * @param message Log message
     */
    public static void v(String tag, String message) {
        if (DEBUG) {
            Log.v(TAG + "_" + tag, message);
        }
    }

    /**
     * Log verbose level message with exception.
     *
     * @param tag Tag
     * @param message Log message
     * @param tr Exception
     */
    public static void v(String tag, String message, Throwable tr) {
        if (DEBUG) {
            Log.v(TAG + "_" + tag, message, tr);
        }
    }

    /**
     * Print hexadecimal data.
     *
     * @param tag Tag
     * @param prefix Prefix
     * @param data Data
     */
    public static void hexDump(String tag, String prefix, byte[] data) {
        if (!DEBUG) {
            return;
        }

        if (data == null) {
            d(tag, prefix + ": null");
            return;
        }

        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            hexString.append(String.format("%02x", data[i]));
            if ((i + 1) % HEX_GROUP_SIZE == 0) {
                hexString.append("\n");
            } else {
                hexString.append(" ");
            }
        }
        d(tag, prefix + ":\n" + hexString.toString());
    }

    /**
     * Log operation step.
     *
     * @param tag Tag
     * @param step Step name
     */
    public static void step(String tag, String step) {
        if (DEBUG) {
            d(tag, "### STEP: " + step + " ###");
        }
    }

    /**
     * Log operation start.
     *
     * @param tag Tag
     * @param operation Operation name
     */
    public static void begin(String tag, String operation) {
        if (DEBUG) {
            d(tag, ">>> BEGIN: " + operation + " <<<");
        }
    }

    /**
     * Log operation end.
     *
     * @param tag Tag
     * @param operation Operation name
     * @param success Whether successful
     */
    public static void end(String tag, String operation, boolean success) {
        if (DEBUG) {
            d(tag, ">>> END: " + operation + " - " + (success ? "SUCCESS" : "FAILED") + " <<<");
        }
    }

    /**
     * Log method entry.
     *
     * @param tag Tag
     * @param method Method name
     */
    public static void methodIn(String tag, String method) {
        if (DEBUG) {
            v(tag, "==> " + method);
        }
    }

    /**
     * Log method exit.
     *
     * @param tag Tag
     * @param method Method name
     */
    public static void methodOut(String tag, String method) {
        if (DEBUG) {
            v(tag, "<== " + method);
        }
    }

    /**
     * Log parameter.
     *
     * @param tag Tag
     * @param paramName Parameter name
     * @param paramValue Parameter value
     */
    public static void param(String tag, String paramName, Object paramValue) {
        if (DEBUG) {
            d(tag, "Param: " + paramName + " = " + (paramValue == null ? "null" : paramValue.toString()));
        }
    }

    /**
     * Log JSON data.
     *
     * @param tag Tag
     * @param description Description
     * @param json JSON string
     */
    public static void json(String tag, String description, String json) {
        if (DEBUG) {
            if (json == null || json.isEmpty()) {
                d(tag, description + ": [EMPTY JSON]");
                return;
            }

            d(tag, description + ":\n" + json);
        }
    }

    /**
     * Log callback interface for UI display.
     */
    public interface LogCallback {
        /**
         * Called when a log message is generated.
         *
         * @param message Log message
         */
        void onLog(String message);
    }

    /**
     * Set log callback.
     *
     * @param callback Callback interface
     */
    public static void setLogCallback(LogCallback callback) {
        sLogCallback = callback;
    }

    /**
     * Log message to UI.
     *
     * @param message Log message
     */
    public static void log(String message) {
        if (sLogCallback != null) {
            sLogCallback.onLog(message);
        }
    }
}