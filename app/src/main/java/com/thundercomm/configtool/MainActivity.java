/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.StatFs;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.gson.JsonArray;
import com.google.gson.JsonParser;
import com.thundercomm.configtool.api.ConfigToolServiceManager;
import com.thundercomm.configtool.api.IConfigToolServiceConnectCallback;
import com.thundercomm.configtool.utils.Constants;
import com.thundercomm.configtool.utils.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 用于测试配置工具服务的主活动类。
 */
public class MainActivity extends Activity {
    private static final String TAG = "MainActivity";

    // 魔法数字常量
    private static final int TEST_NUMBER_VALUE = 100;
    private static final int INT_3 = 3;
    private static final int TEST_PROP2_VALUE = 200;
    private static final int TEST_UPDATED_PROP2_VALUE = 999;

    // 配置常量
    private static final int DEFAULT_BEFORE_EVENT_TIME = 10;
    private static final int DEFAULT_AFTER_EVENT_TIME = 10;
    private static final int DEFAULT_REC_CAM_VALUE = 10;
    private static final int DEFAULT_MIN_STORAGE_MB = 44000;
    private static final int DEFAULT_CHECK_INTERVAL_SECONDS = 30;
    private static final int DEFAULT_MAX_DELETE_SIZE_MB = 1000;

    // 阈值常量
    private static final double TH1_VALUE_1 = 0.5;
    private static final double TH2_VALUE_1 = 0.8;
    private static final double TH1_VALUE_2 = 0.6;
    private static final double TH2_VALUE_2 = 0.9;
    private static final double LOW_VOLTAGE_THRESHOLD = 3.5;

    // 数组值常量
    private static final int WINDOW_VALUE_1 = 100;
    private static final int WINDOW_VALUE_2 = 200;
    private static final int WINDOW_VALUE_3 = 300;
    private static final int WINDOW_VALUE_4 = 150;
    private static final int WINDOW_VALUE_5 = 250;
    private static final int WINDOW_VALUE_6 = 350;

    // UI组件
    private EditText mJsonInput;
    private EditText mKeyInput;
    private Button mSaveButton;
    private Button mGetButton;
    private Button mGenerateTestConfigButton;
    private Button mTestConfigVersionButton;
    private Button mTestEventRectimeButton;
    private Button mTestRecModeButton;
    private Button mTestAppInfoButton;
    private Button mTestFotaInfoButton;

    // 新功能测试按钮
    private Button mTestVersionCompareButton;
    private Button mTestEncryptionRetryButton;
    private Button mTestDynamicSdCardButton;
    private Button mTestEventBrokerButton;
    private Button mTestExternalVersionCheckButton;

    private TextView mLogOutput;
    private ScrollView mScrollView;

    // ConfigTool service
    private ConfigToolServiceManager mConfigManager;
    private boolean mServiceBound = false;

    // ConfigTool service connect callback
    private final IConfigToolServiceConnectCallback mConfigServiceCallback = new IConfigToolServiceConnectCallback() {
        @Override
        public void onServiceConnected() {
            Logger.begin(TAG, "Service connected");
            mServiceBound = true;
            log("配置工具服务已连接");
            Logger.i(TAG, "ConfigToolService connected");

            // Enable UI buttons
            runOnUiThread(() -> {
                Logger.d(TAG, "Enable UI buttons");
                setButtonsEnabled(true);
            });
            Logger.end(TAG, "Service connected", true);
        }

        @Override
        public void onServiceDisconnected() {
            Logger.begin(TAG, "Service disconnected");
            mServiceBound = false;
            log("配置工具服务已断开连接");
            Logger.w(TAG, "ConfigToolService disconnected");

            // Disable UI buttons
            runOnUiThread(() -> {
                Logger.d(TAG, "Disable UI buttons");
                setButtonsEnabled(false);
            });
            Logger.end(TAG, "Service disconnected", true);
        }

        @Override
        public void onConfigChanged(String modifiedFields, String addedFields, long timestamp) {
            Logger.begin(TAG, "Config changed notification received");
            String formattedTime = new SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                    .format(new Date(timestamp));
            log("配置变更通知已接收，时间: " + formattedTime);

            if (modifiedFields != null
                    && !modifiedFields.isEmpty()) {
                log("修改的字段: " + modifiedFields);
                Logger.d(TAG, "Modified fields: " + modifiedFields);
            }

            if (addedFields != null
                    && !addedFields.isEmpty()) {
                log("新增的字段: " + addedFields);
                Logger.d(TAG, "Added fields: " + addedFields);
            }

            Logger.end(TAG, "Config changed notification received", true);
        }
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Logger.begin(TAG, "onCreate");
        setContentView(R.layout.activity_main);

        // Initialize UI components
        Logger.step(TAG, "Initialize UI components");
        mJsonInput = findViewById(R.id.json_input);
        mKeyInput = findViewById(R.id.key_input);
        mSaveButton = findViewById(R.id.save_button);
        mGetButton = findViewById(R.id.get_button);
        mGenerateTestConfigButton = findViewById(R.id.generate_test_config_button);
        mTestConfigVersionButton = findViewById(R.id.test_configVersion_button);
        mTestEventRectimeButton = findViewById(R.id.test_eventRectime_button);
        mTestRecModeButton = findViewById(R.id.test_recMode_button);
        mTestAppInfoButton = findViewById(R.id.test_appInfo_button);
        mTestFotaInfoButton = findViewById(R.id.test_fotaInfo_button);

        // 新功能测试按钮
        mTestVersionCompareButton = findViewById(R.id.test_version_compare_button);
        mTestEncryptionRetryButton = findViewById(R.id.test_encryption_retry_button);
        mTestDynamicSdCardButton = findViewById(R.id.test_dynamic_sdcard_button);
        mTestEventBrokerButton = findViewById(R.id.test_eventbroker_button);
        mTestExternalVersionCheckButton = findViewById(R.id.test_external_version_check_button);

        mLogOutput = findViewById(R.id.log_output);
        mScrollView = findViewById(R.id.scroll_view);



        // Set button click events
        Logger.step(TAG, "Set button click events");
        mSaveButton.setOnClickListener(this::onSaveClick);
        mGetButton.setOnClickListener(this::onGetClick);
        mGenerateTestConfigButton.setOnClickListener(this::onGenerateTestConfigClick);
        mTestConfigVersionButton.setOnClickListener(v -> onTestButtonClick("configVersion"));
        mTestEventRectimeButton.setOnClickListener(v -> onTestButtonClick("eventRectime"));
        mTestRecModeButton.setOnClickListener(v -> onTestButtonClick("recMode"));
        mTestAppInfoButton.setOnClickListener(v -> onTestButtonClick("appInfo"));
        mTestFotaInfoButton.setOnClickListener(v -> onTestButtonClick("fotaTargetInfo"));

        // 新功能测试按钮点击事件
        mTestVersionCompareButton.setOnClickListener(v -> testVersionCompare());
        mTestEncryptionRetryButton.setOnClickListener(v -> testEncryptionRetry());
        mTestDynamicSdCardButton.setOnClickListener(v -> testDynamicSdCard());
        mTestEventBrokerButton.setOnClickListener(v -> testEventBroker());
        mTestExternalVersionCheckButton.setOnClickListener(v -> testExternalVersionCheck());

        // Initially disable buttons
        Logger.step(TAG, "Initially disable buttons");
        setButtonsEnabled(false);

        // No longer try to start FileEncrypt service, just record permission information
        try {
            Logger.d(TAG, "Checking FileEncrypt service permissions");

            // In Android 10 and above, foreground apps cannot directly start background services
            // Changed to only bind but not start the service

            // Permission check removed, no longer needed
            Logger.d(TAG, "FileEncrypt service permissions check removed");

            // Add PackageInfo check
            try {
                PackageInfo packageInfo = getPackageManager().getPackageInfo(Constants.FILE_ENCRYPT_PACKAGE, 0);
                Logger.d(TAG, "FileEncrypt app is installed, version: " + packageInfo.versionName);
                log("FileEncrypt app is installed, version: " + packageInfo.versionName);
            } catch (PackageManager.NameNotFoundException exception) {
                Logger.w(TAG, "FileEncrypt app is not installed", exception);
                log("Warning: FileEncrypt app is not installed");
            }

        } catch (Exception exception) {
            Logger.e(TAG, "Error occurred while processing FileEncrypt service", exception);
        }

        // Initialize ConfigToolServiceManager
        Logger.step(TAG, "Initialize ConfigToolServiceManager");
        mConfigManager = ConfigToolServiceManager.getInstance();
        mConfigManager.setServiceConnectCallback(mConfigServiceCallback);

        // Bind service
        Logger.d(TAG, "Binding ConfigTool service");
        mConfigManager.bindService(this);

        // Set log callback
        Logger.setLogCallback(this::log);
        log("应用程序已启动");
        Logger.end(TAG, "onCreate", true);
    }

    @Override
    protected void onDestroy() {
        Logger.begin(TAG, "onDestroy");
        super.onDestroy();
        // Unbind service
        if (mServiceBound) {
            Logger.d(TAG, "Unbinding service");
            mConfigManager.unbindService(this);
            mServiceBound = false;
        } else {
            Logger.d(TAG, "Service not bound, no need to unbind");
        }

        // Clear Logger callback
        Logger.setLogCallback(null);
        Logger.end(TAG, "onDestroy", true);
    }

    /**
     * Save button click event.
     */
    private void onSaveClick(View view) {
        Logger.begin(TAG, "onSaveClick");
        String jsonInput = mJsonInput.getText().toString().trim();
        if (jsonInput.isEmpty()) {
            log("错误: JSON输入为空");
            Logger.w(TAG, "JSON input is empty");
            Logger.end(TAG, "onSaveClick", false);
            return;
        }

        try {
            // Use ConfigToolServiceManager to save config
            if (mServiceBound) {
                boolean result = mConfigManager.saveConfig(jsonInput);
                if (result) {
                    log("JSON配置保存成功");
                    Logger.i(TAG, "Successfully saved JSON configuration");
                } else {
                    log("JSON配置保存失败");
                    Logger.w(TAG, "Failed to save JSON configuration");
                }
            } else {
                log("错误: 服务未连接");
                Logger.w(TAG, "Service not bound");
            }
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in onSaveClick", exception);
            log("Error saving configuration: " + exception.getMessage());
        }
        Logger.end(TAG, "onSaveClick", true);
    }

    /**
     * Get button click event.
     */
    private void onGetClick(View view) {
        Logger.begin(TAG, "onGetClick");
        String key = mKeyInput.getText().toString().trim();
        if (key.isEmpty()) {
            log("错误: 键名为空");
            Logger.w(TAG, "Key is empty");
            Logger.end(TAG, "onGetClick", false);
            return;
        }

        getConfigValue(key);
        Logger.end(TAG, "onGetClick", true);
    }

    /**
     * Test button click event.
     */
    private void onTestButtonClick(String key) {
        Logger.begin(TAG, "Test button click");
        Logger.param(TAG, "key", key);

        if (!mServiceBound) {
            log("服务未连接");
            Logger.w(TAG, "Service not connected");
            Logger.end(TAG, "Test button click", false);
            return;
        }

        mKeyInput.setText(key);
        Logger.d(TAG, "Set input text: " + key);

        getConfigValue(key);
        Logger.end(TAG, "Test button click", true);
    }

    /**
     * Get config value by key.
     */
    private void getConfigValue(String key) {
        Logger.begin(TAG, "getConfigValue");
        try {
            if (mServiceBound) {
                // Use ConfigToolServiceManager to get config value
                String value = mConfigManager.getConfigValue(key);
                if (value != null) {
                    log("Key: " + key);
                    log("Value: " + formatValue(value));
                    Logger.i(TAG, "Got value for key: " + key);
                } else {
                    log("No value found for key: " + key);
                    Logger.w(TAG, "No value found for key: " + key);
                }
            } else {
                log("Error: Service not bound");
                Logger.w(TAG, "Service not bound");
            }
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in getConfigValue", exception);
            log("Error getting configuration: " + exception.getMessage());
        }
        Logger.end(TAG, "getConfigValue", true);
    }

    /**
     * Generate test configuration button click event.
     */
    private void onGenerateTestConfigClick(View view) {
        Logger.begin(TAG, "Generate test configuration");

        // Generate sample configuration based on uploaded images
        Logger.step(TAG, "Generate sample configuration");
        String testConfig = generateSampleConfig();
        Logger.d(TAG, "Generated configuration length: " + testConfig.length());

        // Set to input box
        Logger.step(TAG, "Set to input box");
        mJsonInput.setText(testConfig);

        // Also create a test configuration file to SD card
        Logger.step(TAG, "Create external test configuration file");
        boolean created = createExternalTestConfigFile(testConfig);
        if (created) {
            log("测试配置文件已创建到SD卡");
        }

        log("测试配置已生成并填入输入框");
        Logger.end(TAG, "Generate test configuration", true);
    }

    /**
     * Generate sample configuration JSON.
     */
    private String generateSampleConfig() {
        return "{\n"
                + "  \"configVersion\": \"V100\",\n"
                + "  \"eventRectime\": {\n"
                + "    \"beforeEvent\": " + DEFAULT_BEFORE_EVENT_TIME + ",\n"
                + "    \"afterEvent\": " + DEFAULT_AFTER_EVENT_TIME + "\n"
                + "  },\n"
                + "  \"recMode\": \"high_q\",\n"
                + "  \"recCam\": " + DEFAULT_REC_CAM_VALUE + ",\n"
                + "  \"movieEnctyption\": true,\n"
                + "  \"inpactDetection\": false,\n"
                + "  \"accThreshold\": [\n"
                + "    {\n"
                + "      \"TH1\": " + TH1_VALUE_1 + ",\n"
                + "      \"TH2\": " + TH2_VALUE_1 + ",\n"
                + "      \"window\": [" + WINDOW_VALUE_1 + ", " + WINDOW_VALUE_2 + ", " + WINDOW_VALUE_3 + "]\n"
                + "    },\n"
                + "    {\n"
                + "      \"TH1\": " + TH1_VALUE_2 + ",\n"
                + "      \"TH2\": " + TH2_VALUE_2 + ",\n"
                + "      \"window\": [" + WINDOW_VALUE_4 + ", " + WINDOW_VALUE_5 + ", " + WINDOW_VALUE_6 + "]\n"
                + "    }\n"
                + "  ],\n"
                + "  \"audioPlay\": false,\n"
                + "  \"audioRec\": false,\n"
                + "  \"parkingRec\": false,\n"
                + "  \"lcdUse\": false,\n"
                + "  \"lowVoltageThreshold\": " + LOW_VOLTAGE_THRESHOLD + ",\n"
                + "  \"loggLv\": \"info\",\n"
                + "  \"aGps\": true,\n"
                + "  \"sim\": \"esim\",\n"
                + "  \"ble\": true,\n"
                + "  \"storageManagement\": {\n"
                + "    \"minStorageMb\": " + DEFAULT_MIN_STORAGE_MB + ",\n"
                + "    \"checkIntervalSeconds\": " + DEFAULT_CHECK_INTERVAL_SECONDS + ",\n"
                + "    \"maxDeleteSizeMb\": " + DEFAULT_MAX_DELETE_SIZE_MB + ",\n"
                + "    \"monitorDirectories\": [\n"
                + "      \"/ext_sdcard/DCIM\",\n"
                + "      \"/ext_sdcard/Send\",\n"
                + "      \"/ext_sdcard/Logs\"\n"
                + "    ]\n"
                + "  },\n"
                + "  \"appInfo\": [\n"
                + "    {\n"
                + "      \"packageName\": \"com.ssol.titanApp\",\n"
                + "      \"permission\": \"POST_NOTIFICATIONS,BLUETOOTH_CONNECT,BLUETOOTH_ADVERTISE,"
                + "ACCESS_COARSE_LOCATION,BLUETOOTH_SCAN,ACCESS_BACKGROUND_LOCATION,ACCESS_FINE_LOCATION\",\n"
                + "      \"autoBoot\": true\n"
                + "    }\n"
                + "  ],\n"
                + "  \"apnInfo\": [\n"
                + "    {\n"
                + "      \"nwType\": \"open\",\n"
                + "      \"apnName\": \"internet\",\n"
                + "      \"userName\": \"user\",\n"
                + "      \"password\": \"pass\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"fotaTergetInfo\": [\n"
                + "    {\n"
                + "      \"fotaNo\": \"2\",\n"
                + "      \"targetName\": \"CS3\",\n"
                + "      \"fotaType\": \"yosemite\",\n"
                + "      \"fotaServer\": \"0\",\n"
                + "      \"userAgent\": \"SSOLMS/1.0(CS3;Unit;000[1.0])\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"fotaNo\": \"21\",\n"
                + "      \"targetName\": \"com.ssol.titanApp\",\n"
                + "      \"fotaType\": \"apk\",\n"
                + "      \"fotaServer\": \"0\",\n"
                + "      \"userAgent\": \"SSOLMS/1.0(CFT5;CFT500;000:[1.0])\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"fotaNo\": \"90\",\n"
                + "      \"targetName\": \"CFT500\",\n"
                + "      \"fotaType\": \"firmware\",\n"
                + "      \"fotaServer\": \"1\",\n"
                + "      \"userAgent\": \"SSOLMS/1.0(CFT5;CFT500;000:[1.0])\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"fotaNo\": \"91\",\n"
                + "      \"targetName\": \"config\",\n"
                + "      \"fotaType\": \"config\",\n"
                + "      \"fotaServer\": \"0\",\n"
                + "      \"userAgent\": \"SSOLMS/1.0(CFT5;CFT500;000:[1.0])\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"fotaServer\": [\n"
                + "    {\n"
                + "      \"no\": \"0\",\n"
                + "      \"nwType\": \"closed\",\n"
                + "      \"url\": \"https://fota.example.com/closed\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"no\": \"1\",\n"
                + "      \"nwType\": \"open\",\n"
                + "      \"url\": \"https://fota.example.com/open\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"rebootTarget\": [\n"
                + "    {\n"
                + "      \"no\": \"0\",\n"
                + "      \"target\": \"drv_system\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"no\": \"1\",\n"
                + "      \"target\": \"com.thundercomm.testapp\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"no\": \"2\",\n"
                + "      \"target\": \"com.ssol.titanApp\"\n"
                + "    }\n"
                + "  ]\n"
                + "}";
    }

    /**
     * Create external test configuration file.
     */
    private boolean createExternalTestConfigFile(String configJson) {
        try {
            // Create directory structure
            String externalConfigPath = Constants.getExternalConfigPath();
            if (externalConfigPath == null) {
                log("External SD card not available, skipping external test config file creation");
                return false;
            }

            File externalDir = new File(externalConfigPath);
            if (!externalDir.exists()) {
                externalDir.mkdirs();
            }

            // Create and write to config file directly in the Config directory
            File configFile = new File(externalDir, Constants.EXTERNAL_CONFIG_FILENAME);
            try (FileOutputStream fos = new FileOutputStream(configFile)) {
                fos.write(configJson.getBytes(StandardCharsets.UTF_8));
            }

            log("Test configuration file created at " + configFile.getAbsolutePath());
            return true;
        } catch (IOException ioException) {
            Logger.e(TAG, "Failed to create external test config file", ioException);
            log("Error creating test config file: " + ioException.getMessage());
            return false;
        }
    }

    /**
     * Log message.
     */
    private void log(String message) {
        Logger.d(TAG, "UI log: " + message);
        runOnUiThread(() -> {
            if (mLogOutput != null) {
                mLogOutput.append(message + "\n");
                if (mScrollView != null) {
                    mScrollView.post(() -> mScrollView.fullScroll(ScrollView.FOCUS_DOWN));
                }
            }
        });
    }

    /**
     * Set all buttons enabled or disabled.
     */
    private void setButtonsEnabled(boolean enabled) {
        runOnUiThread(() -> {
            mSaveButton.setEnabled(enabled);
            mGetButton.setEnabled(enabled);
            mGenerateTestConfigButton.setEnabled(enabled);
            mTestConfigVersionButton.setEnabled(enabled);
            mTestEventRectimeButton.setEnabled(enabled);
            mTestRecModeButton.setEnabled(enabled);
            mTestAppInfoButton.setEnabled(enabled);
            mTestFotaInfoButton.setEnabled(enabled);

            // 新功能测试按钮
            if (mTestVersionCompareButton != null) {
                mTestVersionCompareButton.setEnabled(enabled);
            }
            if (mTestEncryptionRetryButton != null) {
                mTestEncryptionRetryButton.setEnabled(enabled);
            }
            if (mTestDynamicSdCardButton != null) {
                mTestDynamicSdCardButton.setEnabled(enabled);
            }
            if (mTestEventBrokerButton != null) {
                mTestEventBrokerButton.setEnabled(enabled);
            }
            if (mTestExternalVersionCheckButton != null) {
                mTestExternalVersionCheckButton.setEnabled(enabled);
            }
        });
    }

    /**
     * Test encryption service function - test if all JSON configuration items can be read correctly.
     */
    private void testEncryptService() {
        Logger.begin(TAG, "Test encryption service");
        log("Starting encryption service test - testing all JSON configuration items...");

        if (!mServiceBound) {
            log("Error: ConfigTool service not connected");
            Logger.e(TAG, "ConfigTool service not connected");
            Logger.end(TAG, "Test encryption service", false);
            return;
        }

        try {
            // Use sample configuration data
            String sampleConfig = generateSampleConfig();
            log("Using sample configuration for comprehensive encryption and decryption tests...");

            // Parse JSON and save configuration
            if (!prepareAndSaveTestConfig(sampleConfig)) {
                return;
            }

            // Test reading all top-level keys
            testReadAllKeys(sampleConfig);

        } catch (Exception exception) {
            Logger.e(TAG, "Exception in testEncryptService", exception);
            log("Error testing encryption service: " + exception.getMessage());
        }
    }

    /**
     * Prepare and save test configuration
     *
     * @param sampleConfig Sample configuration JSON string
     * @return Returns true if configuration saved successfully, otherwise returns false
     * @throws JSONException If JSON parsing fails
     * @throws RemoteException If remote service call fails
     */
    private boolean prepareAndSaveTestConfig(String sampleConfig) throws JSONException {
        Logger.begin(TAG, "prepareAndSaveTestConfig");
        try {
            if (mServiceBound) {
                boolean result = mConfigManager.saveConfig(sampleConfig);
                Logger.i(TAG, "Config save result: " + result);
                Logger.end(TAG, "prepareAndSaveTestConfig", result);
                return result;
            } else {
                Logger.w(TAG, "Service not bound");
                log("Error: Service not bound");
                Logger.end(TAG, "prepareAndSaveTestConfig", false);
                return false;
            }
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in prepareAndSaveTestConfig", exception);
            log("Error preparing and saving test config: " + exception.getMessage());
            return false;
        }
    }

    /**
     * Test reading all JSON keys
     *
     * @param sampleConfig Sample configuration JSON string
     * @throws JSONException If JSON parsing fails
     * @throws RemoteException If remote service call fails
     */
    private void testReadAllKeys(String sampleConfig) throws JSONException {
        Logger.begin(TAG, "testReadAllKeys");
        try {
            if (!mServiceBound) {
                log("Error: Service not bound");
                Logger.w(TAG, "Service not bound");
                Logger.end(TAG, "testReadAllKeys", false);
                return;
            }

            JSONObject jsonObject = new JSONObject(sampleConfig);
            log("\nTesting individual key retrieval:");
            Logger.i(TAG, "Testing individual key retrieval");

            // Test retrieval of each top-level key
            JSONArray names = jsonObject.names();
            if (names != null) {
                for (int i = 0; i < names.length(); i++) {
                    String key = names.getString(i);
                    String value = mConfigManager.getConfigValue(key);
                    if (value != null) {
                        log("Key: " + key);
                        log("Value: " + formatValue(value));
                        Logger.i(TAG, "Got value for key: " + key);

                        // If the value is a JSON object, test its properties
                        if (value.startsWith("{")) {
                            testObjectProperties(key, value);
                        } else if (value.startsWith("[")) {
                            testArrayItems(key, value);
                        }
                    } else {
                        log("No value found for key: " + key);
                        Logger.w(TAG, "No value found for key: " + key);
                    }
                }
            }
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in testReadAllKeys", exception);
            log("JSON Error: " + exception.getMessage());
        }
        Logger.end(TAG, "testReadAllKeys", true);
    }

    /**
     * Test object properties.
     */
    private void testObjectProperties(String parentKey, String jsonValue) throws JSONException {
        Logger.begin(TAG, "testObjectProperties");
        JSONObject jsonObject = new JSONObject(jsonValue);
        log("\nTesting nested properties of object '" + parentKey + "':");
        Logger.i(TAG, "Testing nested properties of object: " + parentKey);

        JSONArray names = jsonObject.names();
        if (names != null) {
            for (int i = 0; i < names.length(); i++) {
                String key = names.getString(i);
                String fullKey = parentKey + "." + key;
                try {
                    if (mServiceBound) {
                        String value = mConfigManager.getConfigValue(fullKey);
                        if (value != null) {
                            log("Key: " + fullKey);
                            log("Value: " + formatValue(value));
                            Logger.i(TAG, "Got value for key: " + fullKey);
                        } else {
                            log("No value found for key: " + fullKey);
                            Logger.w(TAG, "No value found for key: " + fullKey);
                        }
                    } else {
                        log("Error: Service not bound");
                        Logger.w(TAG, "Service not bound");
                        break;
                    }
                } catch (Exception exception) {
                    Logger.e(TAG, "Exception in testObjectProperties", exception);
                    log("Error retrieving key " + fullKey + ": " + exception.getMessage());
                }
            }
        }
        Logger.end(TAG, "testObjectProperties", true);
    }

    /**
     * Test array items.
     */
    private void testArrayItems(String parentKey, String jsonValue) throws JSONException {
        Logger.begin(TAG, "testArrayItems");
        JSONArray jsonArray = new JSONArray(jsonValue);
        log("\nTesting array items of '" + parentKey + "':");
        Logger.i(TAG, "Testing array items of: " + parentKey);

        for (int i = 0; i < jsonArray.length(); i++) {
            String indexKey = parentKey + "[" + i + "]";
            try {
                if (mServiceBound) {
                    String value = mConfigManager.getConfigValue(indexKey);
                    if (value != null) {
                        log("Key: " + indexKey);
                        log("Value: " + formatValue(value));
                        Logger.i(TAG, "Got value for array item: " + indexKey);

                        // If item is an object, also test its properties
                        if (value.startsWith("{")) {
                            testObjectProperties(indexKey, value);
                        }
                    } else {
                        log("No value found for array item: " + indexKey);
                        Logger.w(TAG, "No value found for array item: " + indexKey);
                    }
                } else {
                    log("Error: Service not bound");
                    Logger.w(TAG, "Service not bound");
                    break;
                }
            } catch (Exception exception) {
                Logger.e(TAG, "Exception in testArrayItems", exception);
                log("Error retrieving array item " + indexKey + ": " + exception.getMessage());
            }
        }
        Logger.end(TAG, "testArrayItems", true);
    }

    /**
     * Format value output
     *
     * @param value JSON value string
     * @return Formatted value string
     */
    private String formatValue(String value) {
        Logger.methodIn(TAG, "formatValue");

        if (value == null) {
            return "null";
        }

        // For short values, return directly
        if (value.length() < Constants.MAX_DISPLAY_TEXT_LENGTH) {
            return value;
        }

        try {
            // For long JSON objects or arrays, show type and size
            if (value.trim().startsWith("{")) {
                JSONObject obj = new JSONObject(value);
                Logger.methodOut(TAG, "formatValue");
                return "[Object, " + obj.length() + " properties]";
            }

            if (value.trim().startsWith("[")) {
                JSONArray arr = new JSONArray(value);
                Logger.methodOut(TAG, "formatValue");
                return "[Array, " + arr.length() + " items]";
            }
        } catch (JSONException exception) {
            // Not a valid JSON object/array
        }

        // Other long strings
        return value.substring(0, Constants.TRUNCATED_TEXT_LENGTH) + "...";
    }

    /**
     * Test merge configuration function.
     */
    private void testMergeConfig() {
        log("\n--- Testing Config Merge Function ---");

        try {
            if (!mServiceBound) {
                log("Error: Service not bound");
                return;
            }

            // Save base config
            JSONObject baseConfig = createBaseConfig();
            log("Saving base configuration...");
            if (!mConfigManager.saveConfig(baseConfig.toString())) {
                log("Failed to save base configuration!");
                return;
            }
            log("Base configuration saved successfully");

            // Save update config
            JSONObject updateConfig = createUpdateConfig();
            log("Applying update configuration (merge)...");
            if (!mConfigManager.saveConfig(updateConfig.toString())) {
                log("Failed to save update configuration!");
                return;
            }
            log("Update configuration saved successfully");

            // Verify final merged state
            log("\nVerifying final merged configuration...");
            verifyMergedConfig();

            log("--- Config Merge Test Completed ---");
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in testMergeConfig", exception);
            log("Error testing merge config: " + exception.getMessage());
        }
    }

    /**
     * Create base config for merge test.
     */
    private JSONObject createBaseConfig() throws JSONException {
        JSONObject baseConfig = new JSONObject();

        // Add base values
        baseConfig.put("testString", "base string value");
        baseConfig.put("testNumber", TEST_NUMBER_VALUE);
        baseConfig.put("testBoolean", true);

        // Create a nested object
        JSONObject nestedObject = new JSONObject();
        nestedObject.put("prop1", "base property 1");
        nestedObject.put("prop2", TEST_PROP2_VALUE);
        nestedObject.put("prop3", false);
        baseConfig.put("testObject", nestedObject);

        // Create an array
        JSONArray testArray = new JSONArray();
        testArray.put("item1");
        testArray.put("item2");
        testArray.put("item3");
        baseConfig.put("testArray", testArray);

        return baseConfig;
    }

    /**
     * Create update config for merge test.
     */
    private JSONObject createUpdateConfig() throws JSONException {
        JSONObject updateConfig = new JSONObject();

        // Add/update some values
        updateConfig.put("testString", "updated string value");
        updateConfig.put("testNewValue", "completely new value");

        // Update nested object (partial)
        JSONObject updateNestedObject = new JSONObject();
        updateNestedObject.put("prop2", TEST_UPDATED_PROP2_VALUE);
        updateNestedObject.put("prop4", "new nested property");
        updateConfig.put("testObject", updateNestedObject);

        // Update array (complete replacement)
        JSONArray updateArray = new JSONArray();
        updateArray.put("new item1");
        updateArray.put("new item2");
        updateConfig.put("testArray", updateArray);

        return updateConfig;
    }

    /**
     * Verify the merged configuration results.
     */
    private void verifyMergedConfig() {
        try {
            if (!mServiceBound) {
                log("Error: Service not bound");
                return;
            }

            verifySimpleValues();
            verifyNestedObjectValues();
            verifyArrayValues();
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in verifyMergedConfig", exception);
            log("Error verifying merged configuration: " + exception.getMessage());
        }
    }

    /**
     * Verify simple key-value pairs in merged config.
     */
    private void verifySimpleValues() {
        // Check updated string value
        String testString = mConfigManager.getConfigValue("testString");
        log("testString: " + testString + " (Expected: updated string value)");

        // Check unchanged number value
        String testNumber = mConfigManager.getConfigValue("testNumber");
        log("testNumber: " + testNumber + " (Expected: " + TEST_NUMBER_VALUE + ")");

        // Check unchanged boolean value
        String testBoolean = mConfigManager.getConfigValue("testBoolean");
        log("testBoolean: " + testBoolean + " (Expected: true)");

        // Check new value
        String testNewValue = mConfigManager.getConfigValue("testNewValue");
        log("testNewValue: " + testNewValue + " (Expected: completely new value)");
    }

    /**
     * Verify nested object values in merged config.
     */
    private void verifyNestedObjectValues() {
        // Check nested object
        String testObject = mConfigManager.getConfigValue("testObject");
        log("testObject: " + formatValue(testObject));

        // Check specific nested properties
        String prop1 = mConfigManager.getConfigValue("testObject.prop1");
        log("testObject.prop1: " + prop1 + " (Expected: base property 1 - unchanged)");

        String prop2 = mConfigManager.getConfigValue("testObject.prop2");
        log("testObject.prop2: " + prop2 + " (Expected: " + TEST_UPDATED_PROP2_VALUE + " - updated)");

        String prop3 = mConfigManager.getConfigValue("testObject.prop3");
        log("testObject.prop3: " + prop3 + " (Expected: false - unchanged)");

        String prop4 = mConfigManager.getConfigValue("testObject.prop4");
        log("testObject.prop4: " + prop4 + " (Expected: new nested property - new)");
    }

    /**
     * Verify array values in merged config.
     */
    private void verifyArrayValues() {
        // Check array (should be completely replaced)
        String testArray = mConfigManager.getConfigValue("testArray");
        log("testArray: " + formatValue(testArray) + " (Expected: completely replaced)");
    }

    /**
     * Register broadcast receiver to monitor configuration changes.
     */
    private void registerConfigChangedReceiver() {
        Logger.begin(TAG, "Register config changed receiver");
        // ConfigToolServiceManager now handles broadcasts automatically, no need to register here
        Logger.i(TAG, "Config changed broadcast is now handled by ConfigToolServiceManager");
        Logger.end(TAG, "Register config changed receiver", true);
    }

    /**
     * Test modify configuration function.
     * This method tests modifying existing parameters and adding new parameters.
     */
    private void testModifyConfig() {
        log("\n--- Testing Configuration Modification Function ---");

        try {
            if (!mServiceBound) {
                log("Error: Service not connected");
                return;
            }

            // First save the base configuration
            String baseConfig = generateModifiedSampleConfig();
            log("Saving base configuration...");
            if (!mConfigManager.saveConfig(baseConfig)) {
                log("Failed to save base configuration!");
                return;
            }
            log("Base configuration saved successfully");

            // Get and verify initial configuration
            log("\nVerifying initial configuration parameters...");
            verifyModifiedConfig();

            // Modify configuration again and add new parameters
            String updatedConfig = generateUpdatedSampleConfig();
            log("\nApplying updated configuration (modifications and new parameters)...");
            if (!mConfigManager.saveConfig(updatedConfig)) {
                log("Failed to save updated configuration!");
                return;
            }
            log("Updated configuration saved successfully");

            // Verify the final merged state
            log("\nVerifying final merged configuration...");
            verifyUpdatedConfig();

            log("--- Configuration Modification Test Completed ---");
        } catch (Exception exception) {
            Logger.e(TAG, "Exception occurred during configuration modification test", exception);
            log("Error testing configuration modification: " + exception.getMessage());
        }
    }

    /**
     * Generate a modified sample configuration JSON.
     */
    private String generateModifiedSampleConfig() {
        return "{\n"
                + "  \"configVersion\": \"V200\",\n"
                + "  \"eventRectime\": {\n"
                + "    \"beforeEvent\": 15,\n"
                + "    \"afterEvent\": 20\n"
                + "  },\n"
                + "  \"recMode\": \"ultra_hd\",\n"
                + "  \"recCam\": 20,\n"
                + "  \"movieEnctyption\": true,\n"
                + "  \"inpactDetection\": true,\n"
                + "  \"accThreshold\": [\n"
                + "    {\n"
                + "      \"TH1\": 0.6,\n"
                + "      \"TH2\": 0.9,\n"
                + "      \"window\": [150, 250, 350]\n"
                + "    },\n"
                + "    {\n"
                + "      \"TH1\": 0.7,\n"
                + "      \"TH2\": 1.0,\n"
                + "      \"window\": [200, 300, 400]\n"
                + "    }\n"
                + "  ],\n"
                + "  \"audioPlay\": true,\n"
                + "  \"audioRec\": true,\n"
                + "  \"parkingRec\": true,\n"
                + "  \"lcdUse\": true,\n"
                + "  \"lowVoltageThreshold\": 3.7,\n"
                + "  \"loggLv\": \"debug\",\n"
                + "  \"aGps\": true,\n"
                + "  \"sim\": \"physical\",\n"
                + "  \"ble\": true,\n"
                + "  \"storageManagement\": {\n"
                + "    \"minStorageMb\": 50000,\n"
                + "    \"checkIntervalSeconds\": 60,\n"
                + "    \"maxDeleteSizeMb\": 2000,\n"
                + "    \"monitorDirectories\": [\n"
                + "      \"/ext_sdcard/DCIM\",\n"
                + "      \"/ext_sdcard/Send\",\n"
                + "      \"/ext_sdcard/Logs\",\n"
                + "      \"/ext_sdcard/Backup\"\n"
                + "    ]\n"
                + "  },\n"
                + "  \"appInfo\": [\n"
                + "    {\n"
                + "      \"packageName\": \"com.ssol.titanApp\",\n"
                + "      \"permission\": \"POST_NOTIFICATIONS,BLUETOOTH_CONNECT,BLUETOOTH_ADVERTISE,"
                + "ACCESS_COARSE_LOCATION,BLUETOOTH_SCAN,ACCESS_BACKGROUND_LOCATION,ACCESS_FINE_LOCATION\",\n"
                + "      \"autoBoot\": true\n"
                + "    },\n"
                + "    {\n"
                + "      \"packageName\": \"com.thundercomm.configtool\",\n"
                + "      \"permission\": \"POST_NOTIFICATIONS,READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE\",\n"
                + "      \"autoBoot\": true\n"
                + "    }\n"
                + "  ],\n"
                + "  \"apnInfo\": [\n"
                + "    {\n"
                + "      \"nwType\": \"closed\",\n"
                + "      \"apnName\": \"private\",\n"
                + "      \"userName\": \"admin\",\n"
                + "      \"password\": \"secure\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"fotaTergetInfo\": [\n"
                + "    {\n"
                + "      \"fotaNo\": \"2\",\n"
                + "      \"targetName\": \"CS4\",\n"
                + "      \"fotaType\": \"sequoia\",\n"
                + "      \"fotaServer\": \"0\",\n"
                + "      \"userAgent\": \"SSOLMS/2.0(CS4;Unit;000[2.0])\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"fotaNo\": \"21\",\n"
                + "      \"targetName\": \"com.ssol.titanApp\",\n"
                + "      \"fotaType\": \"apk\",\n"
                + "      \"fotaServer\": \"0\",\n"
                + "      \"userAgent\": \"SSOLMS/2.0(CFT5;CFT500;000:[2.0])\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"fotaNo\": \"90\",\n"
                + "      \"targetName\": \"CFT500\",\n"
                + "      \"fotaType\": \"firmware\",\n"
                + "      \"fotaServer\": \"1\",\n"
                + "      \"userAgent\": \"SSOLMS/2.0(CFT5;CFT500;000:[2.0])\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"fotaServer\": [\n"
                + "    {\n"
                + "      \"no\": \"0\",\n"
                + "      \"nwType\": \"closed\",\n"
                + "      \"url\": \"https://fota.example.com/closed/v2\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"no\": \"1\",\n"
                + "      \"nwType\": \"open\",\n"
                + "      \"url\": \"https://fota.example.com/open/v2\"\n"
                + "    }\n"
                + "  ]\n"
                + "}";
    }

    /**
     * Generate an updated sample configuration JSON with modifications and new parameters.
     */
    private String generateUpdatedSampleConfig() {
        return "{\n"
                + "  \"configVersion\": \"V300\",\n"
                + "  \"eventRectime\": {\n"
                + "    \"beforeEvent\": 30,\n"
                + "    \"afterEvent\": 45,\n"
                + "    \"maxRecordingTime\": 120\n"
                + "  },\n"
                + "  \"recMode\": \"ai_enhanced\",\n"
                + "  \"recCam\": 30,\n"
                + "  \"storageManagement\": {\n"
                + "    \"minStorageMb\": 60000,\n"
                + "    \"autoBackup\": true,\n"
                + "    \"backupInterval\": 86400\n"
                + "  },\n"
                + "  \"networkSettings\": {\n"
                + "    \"autoConnect\": true,\n"
                + "    \"preferMobile\": false,\n"
                + "    \"reconnectAttempts\": 5\n"
                + "  },\n"
                + "  \"appInfo\": [\n"
                + "    {\n"
                + "      \"packageName\": \"com.thundercomm.configtool\",\n"
                + "      \"permission\": \"POST_NOTIFICATIONS,READ_EXTERNAL_STORAGE,WRITE_EXTERNAL_STORAGE,CAMERA\",\n"
                + "      \"autoBoot\": true,\n"
                + "      \"priority\": \"high\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"packageName\": \"com.thundercomm.newapp\",\n"
                + "      \"permission\": \"BASIC\",\n"
                + "      \"autoBoot\": false,\n"
                + "      \"priority\": \"normal\"\n"
                + "    }\n"
                + "  ],\n"
                + "  \"deviceProfiles\": [\n"
                + "    {\n"
                + "      \"profileId\": \"standard\",\n"
                + "      \"description\": \"Standard Usage Configuration\",\n"
                + "      \"settings\": {\n"
                + "        \"powerSaving\": false,\n"
                + "        \"performanceMode\": \"balanced\"\n"
                + "      }\n"
                + "    },\n"
                + "    {\n"
                + "      \"profileId\": \"eco\",\n"
                + "      \"description\": \"Power Saving Mode Configuration\",\n"
                + "      \"settings\": {\n"
                + "        \"powerSaving\": true,\n"
                + "        \"performanceMode\": \"efficiency\"\n"
                + "      }\n"
                + "    }\n"
                + "  ]\n"
                + "}";
    }

    /**
     * Verify the modified configuration results.
     */
    private void verifyModifiedConfig() {
        try {
            if (!mServiceBound) {
                log("Error: Service not connected");
                return;
            }

            // Check modified parameter values
            String configVersion = mConfigManager.getConfigValue("configVersion");
            log("configVersion: " + configVersion + " (Expected: V200)");

            String beforeEvent = mConfigManager.getConfigValue("eventRectime.beforeEvent");
            log("eventRectime.beforeEvent: " + beforeEvent + " (Expected: 15)");

            String afterEvent = mConfigManager.getConfigValue("eventRectime.afterEvent");
            log("eventRectime.afterEvent: " + afterEvent + " (Expected: 20)");

            String recMode = mConfigManager.getConfigValue("recMode");
            log("recMode: " + recMode + " (Expected: ultra_hd)");

            String recCam = mConfigManager.getConfigValue("recCam");
            log("recCam: " + recCam + " (Expected: 20)");

            String minStorageMb = mConfigManager.getConfigValue("storageManagement.minStorageMb");
            log("storageManagement.minStorageMb: " + minStorageMb + " (Expected: 50000)");

            String checkIntervalSeconds = mConfigManager.getConfigValue("storageManagement.checkIntervalSeconds");
            log("storageManagement.checkIntervalSeconds: " + checkIntervalSeconds + " (Expected: 60)");

            // Check array by getting specific index elements
            // Get appInfo[0] and appInfo[1] to verify array has 2 elements
            String appInfo0 = mConfigManager.getConfigValue("appInfo[0]");
            String appInfo1 = mConfigManager.getConfigValue("appInfo[1]");

            String appInfoPackage = mConfigManager.getConfigValue("appInfo[1].packageName");
            log("appInfo[1].packageName: " + appInfoPackage + " (Expected: com.thundercomm.configtool)");

        } catch (Exception exception) {
            Logger.e(TAG, "Exception occurred during modified configuration verification", exception);
            log("Error verifying modified configuration: " + exception.getMessage());
        }
    }

    /**
     * Verify the updated configuration results after modifications and additions.
     */
    private void verifyUpdatedConfig() {
        try {
            if (!mServiceBound) {
                log("Error: Service not connected");
                return;
            }

            // Check updated parameter values
            String configVersion = mConfigManager.getConfigValue("configVersion");
            log("configVersion: " + configVersion + " (Expected: V300)");

            String beforeEvent = mConfigManager.getConfigValue("eventRectime.beforeEvent");
            log("eventRectime.beforeEvent: " + beforeEvent + " (Expected: 30)");

            String afterEvent = mConfigManager.getConfigValue("eventRectime.afterEvent");
            log("eventRectime.afterEvent: " + afterEvent + " (Expected: 45)");

            String maxRecordingTime = mConfigManager.getConfigValue("eventRectime.maxRecordingTime");
            log("eventRectime.maxRecordingTime: " + maxRecordingTime + " (Expected: 120 - new parameter)");

            String recMode = mConfigManager.getConfigValue("recMode");
            log("recMode: " + recMode + " (Expected: ai_enhanced)");

            String minStorageMb = mConfigManager.getConfigValue("storageManagement.minStorageMb");
            log("storageManagement.minStorageMb: " + minStorageMb + " (Expected: 60000)");

            String autoBackup = mConfigManager.getConfigValue("storageManagement.autoBackup");
            log("storageManagement.autoBackup: " + autoBackup + " (Expected: true - new parameter)");

            String backupInterval = mConfigManager.getConfigValue("storageManagement.backupInterval");
            log("storageManagement.backupInterval: " + backupInterval + " (Expected: 86400 - new parameter)");

            // Check new network settings
            String autoConnect = mConfigManager.getConfigValue("networkSettings.autoConnect");
            log("networkSettings.autoConnect: " + autoConnect + " (Expected: true - new parameter)");

            String preferMobile = mConfigManager.getConfigValue("networkSettings.preferMobile");
            log("networkSettings.preferMobile: " + preferMobile + " (Expected: false - new parameter)");

            // Check new application info fields - using direct element access instead of .length
            String appInfo0 = mConfigManager.getConfigValue("appInfo[0]");
            String appInfo1 = mConfigManager.getConfigValue("appInfo[1]");
            String appInfo2 = mConfigManager.getConfigValue("appInfo[2]"); 
            // Try to get non-existent element, should return null or error

            int appInfoCount = 0;

            log("appInfo array element count: " + appInfoCount + " (Expected: 2)");

            String newAppPackage = mConfigManager.getConfigValue("appInfo[1].packageName");
            log("appInfo[1].packageName: " + newAppPackage + " (Expected: com.thundercomm.newapp)");

            String priority = mConfigManager.getConfigValue("appInfo[0].priority");
            log("appInfo[0].priority: " + priority + " (Expected: high - new field)");

            // Check new device profiles - also using direct element access instead of .length
            String deviceProfile0 = mConfigManager.getConfigValue("deviceProfiles[0]");
            String deviceProfile1 = mConfigManager.getConfigValue("deviceProfiles[1]");
            String deviceProfile2 = mConfigManager.getConfigValue("deviceProfiles[2]"); 
            // Try to get non-existent element

            int profileCount = 0;

            String profileDescription = mConfigManager.getConfigValue("deviceProfiles[0].description");
            log("deviceProfiles[0].description: " + profileDescription + " (Expected: Standard Usage Configuration)");

            String performanceMode = mConfigManager.getConfigValue("deviceProfiles[1].settings.performanceMode");
            log("deviceProfiles[1].settings.performanceMode: " + performanceMode + " (Expected: efficiency)");

        } catch (Exception exception) {
            Logger.e(TAG, "Exception occurred during updated configuration verification", exception);
            log("Error verifying updated configuration: " + exception.getMessage());
        }
    }

    /**
     * Test SD card configuration.
     */
    private void testSdCardConfig() {
        Logger.begin(TAG, "testSdCardConfig");
        log("\n--- Testing SD Card Config Import ---");

        try {
            if (!mServiceBound) {
                log("Error: Service not bound");
                Logger.w(TAG, "Service not bound");
                Logger.end(TAG, "testSdCardConfig", false);
                return;
            }

            // Prepare the external SD card directory
            File sdCardDir = prepareExternalSdCardDirectory();
            if (sdCardDir == null) {
                log("Failed to access external storage");
                Logger.w(TAG, "Failed to access external storage");
                Logger.end(TAG, "testSdCardConfig", false);
                return;
            }

            // Create test JSON config on SD card
            createTestExternalJsonConfig();

            // Delete internal configuration and restart service to trigger auto-import
            deleteInternalConfigAndRestartService();

            // Register for config changed broadcasts
            registerConfigChangedReceiver();

            // Add delayed call to checkTestResults
            log("\nWaiting 8 seconds for service to complete configuration loading...");
            new Handler().postDelayed(this::checkTestResults, Constants.CONFIG_LOAD_WAIT_TIME_MS);

            Logger.end(TAG, "testSdCardConfig", true);
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in testSdCardConfig", exception);
            log("Error testing SD card config: " + exception.getMessage());
        }
    }

    /**
     * Check and prepare external SD card directory.
     */
    private File prepareExternalSdCardDirectory() {
        // 1. Check external SD card directory
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            log("External SD card not available");
            return null;
        }

        File extRoot = new File(externalConfigPath);
        log("External SD card config path: " + extRoot.getAbsolutePath());

        if (!extRoot.exists()) {
            boolean mkdirResult = extRoot.mkdirs();
            log("Create external SD card config directory result: " + (mkdirResult ? "success" : "failed"));
        } else {
            log("External SD card config directory already exists");
        }

        // 2. Check configuration files
        File configJsonFile = new File(extRoot, Constants.EXTERNAL_CONFIG_FILENAME);
        File configBinFile = new File(extRoot, Constants.ENCRYPTED_CONFIG_FILENAME);

        // If old JSON file exists, delete it first
        if (configJsonFile.exists()) {
            log("Found existing JSON config file, attempting to delete...");
            boolean canWrite = configJsonFile.canWrite();
            log("JSON file writable: " + canWrite);

            if (!canWrite) {
                boolean setWritableResult = configJsonFile.setWritable(true);
                log("Set file writable result: " + setWritableResult);
            }

            boolean deleteResult = configJsonFile.delete();
            log("Delete existing JSON file result: " + deleteResult);
        }

        log("Checking external SD card configuration files:");
        return extRoot;
    }

    /**
     * Create test pre-installed JSON configuration.
     */
    private void createTestExternalJsonConfig() {
        Logger.begin(TAG, "createTestExternalJsonConfig");
        log("\nCreating simulated pre-installed external JSON configuration file...");
        String testConfig = generateSampleConfig();

        try {
            // Ensure external directory exists
            String externalConfigPath = Constants.getExternalConfigPath();
            if (externalConfigPath == null) {
                log("External SD card not available, skipping external config creation");
                return; // This is OK to return here as it's just creating external backup
            }

            File extRoot = new File(externalConfigPath);
            if (!extRoot.exists()) {
                boolean mkdirResult = extRoot.mkdirs();
                log("Create directory result: " + mkdirResult);
                if (!mkdirResult) {
                    // Try using app's external storage
                    extRoot = getExternalFilesDir(null);
                    log("Falling back to app external directory: " + extRoot);
                    if (extRoot == null || !extRoot.exists()) {
                        log("Failed to create external directory");
                        Logger.e(TAG, "Failed to create external directory");
                        Logger.end(TAG, "createTestExternalJsonConfig", false);
                        return;
                    }
                }
            }

            // Create configuration file
            File configJsonFile = new File(extRoot, Constants.EXTERNAL_CONFIG_FILENAME);

            // If file already exists, delete it first
            if (configJsonFile.exists()) {
                boolean deleteResult = configJsonFile.delete();
                log("Delete existing file result: " + deleteResult);
                if (!deleteResult) {
                    log("WARNING: Could not delete existing file");
                }
            }

            // Create new file
            try (FileOutputStream fos = new FileOutputStream(configJsonFile)) {
                byte[] bytes = testConfig.getBytes(StandardCharsets.UTF_8);
                fos.write(bytes);
                fos.flush(); // Ensure all content is written to file
                log("Pre-installed config file created successfully (" + bytes.length + " bytes)");
                Logger.i(TAG, "Created external JSON config: " + configJsonFile.getAbsolutePath());

                // Check if file was actually created
                if (configJsonFile.exists()
                        && configJsonFile.length() > 0) {
                    log("File successfully created and contains data");
                } else {
                    log("WARNING: File appears to not exist or is empty after write operation");
                }

                // Set file permissions
                boolean setReadable = configJsonFile.setReadable(true, false);
                boolean setWritable = configJsonFile.setWritable(true, false);
                log("Set file permissions: readable=" + setReadable + ", writable=" + setWritable);

                // Check file permissions
                log("File permissions: canRead=" + configJsonFile.canRead()
                        + ", canWrite=" + configJsonFile.canWrite());
            }
        } catch (IOException ioException) {
            Logger.e(TAG, "Failed to create pre-installed config file", ioException);
            log("Failed to create pre-installed config file: " + ioException.getMessage());
            Logger.end(TAG, "createTestExternalJsonConfig", false);
            return;
        }

        Logger.end(TAG, "createTestExternalJsonConfig", true);
    }

    /**
     * Delete internal configuration file and restart service.
     */
    private void deleteInternalConfigAndRestartService() {
        Logger.begin(TAG, "deleteInternalConfigAndRestartService");

        // Delete internal configuration file
        File internalConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
        log("\nChecking internal configuration file: " + internalConfigFile.getAbsolutePath());

        if (internalConfigFile.exists()) {
            log("Internal config file exists, size: " + internalConfigFile.length() + " bytes");
            boolean deleteResult = internalConfigFile.delete();
            log("Delete internal configuration file result: " + (deleteResult ? "success" : "failed"));

            if (!deleteResult) {
                log("WARNING: Failed to delete internal configuration file");
            }
        } else {
            log("Internal configuration file does not exist");
        }

        log("\nRestarting service to trigger loading from external...");

        // Unbind service first
        if (mServiceBound) {
            log("Unbinding service");
            mConfigManager.unbindService(this);
            mServiceBound = false;
        }

        // Stop service
        log("Stopping service");
        stopService(new Intent(this, ConfigToolService.class));

        // Wait briefly to ensure service has completely stopped
        try {
            Thread.sleep(Constants.CONNECTION_RETRY_DELAY_MS);
        } catch (InterruptedException interruptedException) {
            Logger.e(TAG, "Interrupted while sleeping", interruptedException);
            Thread.currentThread().interrupt();
        }

        // Restart service
        log("Starting service");
        Intent serviceIntent = new Intent(this, ConfigToolService.class);
        startService(serviceIntent);

        // Bind service
        log("Binding service");
        mConfigManager = ConfigToolServiceManager.getInstance(); // Ensure using the latest instance
        mConfigManager.setServiceConnectCallback(mConfigServiceCallback); // Reset callbacks
        mConfigManager.bindService(this);

        log("Service restart initiated");
        Logger.end(TAG, "deleteInternalConfigAndRestartService", true);
    }

    /**
     * Check test results after service has restarted.
     */
    private void checkTestResults() {
        Logger.begin(TAG, "checkTestResults");
        try {
            // Check if external JSON file has been deleted
            log("\nChecking if external JSON file has been deleted:");

            String externalConfigPath = Constants.getExternalConfigPath();
            if (externalConfigPath == null) {
                log("External SD card not available");
                return;
            }

            File extRoot = new File(externalConfigPath);
            File configJsonFile = new File(extRoot, Constants.EXTERNAL_CONFIG_FILENAME);
            File appExtRoot = getExternalFilesDir(null);
            File appConfigJsonFile = null;

            if (appExtRoot != null) {
                appConfigJsonFile = new File(appExtRoot, Constants.EXTERNAL_CONFIG_FILENAME);
            }

            if (configJsonFile.exists()) {
                log("- config.json: still exists (size: " + configJsonFile.length() + " bytes)");
                // If config file wasn't deleted by service, delete it manually
                boolean deleteResult = configJsonFile.delete();
                log("Manual deletion result: " + deleteResult);
            } else {
                log("- config.json: has been deleted (correct)");
            }

            // Check app's external storage config file
            if (appConfigJsonFile != null
                    && appConfigJsonFile.exists()) {
                log("- App external config.json: still exists (size: " + appConfigJsonFile.length() + " bytes)");
                appConfigJsonFile.delete();
            }

            // Test configuration reading
            log("\nTesting configuration reading:");
            String configVersion = mConfigManager.getConfigValue("configVersion");
            log("configVersion: " + (configVersion != null ? configVersion : "not found"));

            // Check internal configuration file
            File internalConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
            log("\nChecking internal storage configuration file:");

            log("\nExternal SD card configuration test completed");
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in checkTestResults", exception);
            log("Error checking test results: " + exception.getMessage());
        }

        Logger.end(TAG, "checkTestResults", true);
    }

    /**
     * Unregister broadcast receiver.
     */
    private void unregisterConfigChangedReceiver() {
        Logger.begin(TAG, "Unregister config changed receiver");

        try {
            // The broadcast receiver is now handled by ConfigToolServiceManager's callback
            // No need to unregister a separate receiver here.
            Logger.i(TAG, "Config changed broadcast receiver is now handled by ConfigToolServiceManager's callback.");
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in unregisterConfigChangedReceiver", exception);
            log("Error unregistering config changed receiver: " + exception.getMessage());
        }
    }

    /**
     * Test storage management configuration.
     */
    private void testStorageManagement() {
        Logger.begin(TAG, "Test storage management configuration");
        log("Testing storage management configuration...");

        if (!mServiceBound) {
            log("Service not connected");
            Logger.w(TAG, "Service not connected");
            Logger.end(TAG, "Test storage management configuration", false);
            return;
        }

        // Test minStorageMb
        String minStorageMb = mConfigManager.getConfigValue("storageManagement.minStorageMb");
        log("minStorageMb: " + formatValue(minStorageMb));

        // Test checkIntervalSeconds
        String checkIntervalSeconds = mConfigManager.getConfigValue("storageManagement.checkIntervalSeconds");
        log("checkIntervalSeconds: " + formatValue(checkIntervalSeconds));

        // Test maxDeleteSizeMb
        String maxDeleteSizeMb = mConfigManager.getConfigValue("storageManagement.maxDeleteSizeMb");
        log("maxDeleteSizeMb: " + formatValue(maxDeleteSizeMb));

        // Test monitorDirectories
        String monitorDirectories = mConfigManager.getConfigValue("storageManagement.monitorDirectories");
        log("monitorDirectories: " + formatValue(monitorDirectories));

        // Test individual directories
        String firstDirectory = mConfigManager.getConfigValue("storageManagement.monitorDirectories[0]");
        log("First directory: " + formatValue(firstDirectory));

        String secondDirectory = mConfigManager.getConfigValue("storageManagement.monitorDirectories[1]");
        log("Second directory: " + formatValue(secondDirectory));

        String thirdDirectory = mConfigManager.getConfigValue("storageManagement.monitorDirectories[2]");
        log("Third directory: " + formatValue(thirdDirectory));

        // Demonstrate example usage of these values
        demonstrateStorageManagementUsage(minStorageMb, checkIntervalSeconds, maxDeleteSizeMb, monitorDirectories);

        log("Storage management configuration test completed successfully");
        Logger.end(TAG, "Test storage management configuration", true);
    }

    /**
     * Demonstrate how to use storage management configuration values.
     *
     * @param minStorageMbStr Minimum storage in MB
     * @param checkIntervalSecondsStr Check interval in seconds
     * @param maxDeleteSizeMbStr Maximum delete size in MB
     * @param monitorDirectoriesStr Monitor directories JSON array
     */
    private void demonstrateStorageManagementUsage(String minStorageMbStr, String checkIntervalSecondsStr, 
                                        String maxDeleteSizeMbStr, String monitorDirectoriesStr) {
        Logger.begin(TAG, "demonstrateStorageManagementUsage");

        try {
            if (!mServiceBound) {
                log("Error: Service not bound");
                Logger.w(TAG, "Service not bound");
                Logger.end(TAG, "demonstrateStorageManagementUsage", false);
                return;
            }

            // Parse parameters
            int minStorageMb = Integer.parseInt(minStorageMbStr);
            int checkIntervalSeconds = Integer.parseInt(checkIntervalSecondsStr);
            int maxDeleteSizeMb = Integer.parseInt(maxDeleteSizeMbStr);

            // Create configuration
            final JSONObject storageConfig = new JSONObject();

            // Create storage management section
            JSONObject storageManagement = new JSONObject();
            storageManagement.put("enabled", true);
            storageManagement.put("minStorageMB", minStorageMb);
            storageManagement.put("checkIntervalSeconds", checkIntervalSeconds);
            storageManagement.put("maxDeleteSizeMB", maxDeleteSizeMb);

            // Parse and add monitor directories
            JSONArray monitorDirs = new JSONArray();
            String[] dirArray = monitorDirectoriesStr.split(",");
            for (String dir : dirArray) {
                dir = dir.trim();
                if (!dir.isEmpty()) {
                    monitorDirs.put(dir);
                }
            }
            storageManagement.put("monitorDirectories", monitorDirs);

            // Add to main config
            storageConfig.put("storageManagement", storageManagement);

            // Save configuration
            log("Saving storage management configuration...");
            boolean result = mConfigManager.saveConfig(storageConfig.toString());

            if (result) {
                log("Storage management configuration saved successfully");

                // Get current free space
                long freeSpace = getFreeStorageSpace();
                log("Current free space: " + (freeSpace / Constants.BYTES_PER_MB) + " MB");

                // Get the saved configuration
                String savedConfig = mConfigManager.getConfigValue("storageManagement");
                if (savedConfig != null) {
                    log("Saved storage management configuration:");
                    log(formatValue(savedConfig));
                } else {
                    log("Failed to retrieve saved storage management configuration");
                }
            } else {
                log("Failed to save storage management configuration");
            }

            Logger.end(TAG, "demonstrateStorageManagementUsage", result);
        } catch (Exception exception) {
            Logger.e(TAG, "Exception in demonstrateStorageManagementUsage", exception);
            log("Error demonstrating storage management usage: " + exception.getMessage());
        }
    }

    /**
     * Get free storage space on device.
     *
     * @return Available space in bytes
     */
    private long getFreeStorageSpace() {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSizeLong();
        long availableBlocks = stat.getAvailableBlocksLong();
        long freeSpace = availableBlocks * blockSize;

        log("当前可用存储空间: " + (freeSpace / Constants.BYTES_PER_MB) + " MB");

        return freeSpace;
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建重复字符串（兼容Java 8）.
     */
    private String createRepeatedString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    // ==================== 新功能测试方法 ====================

    /**
     * 测试版本比对功能.
     */
    private void testVersionCompare() {
        log("\n=== 测试版本比对功能 ===");

        try {
            // 获取当前配置版本
            String currentVersion = mConfigManager.getConfigValue("configVersion");
            log("当前配置版本: " + currentVersion);

            // 创建测试用的外部配置文件，版本更高
            String testConfigJson = "{\n"
                    + "  \"configVersion\": \"V999\",\n"
                    + "  \"testField\": \"版本比对测试\",\n"
                    + "  \"timestamp\": " + System.currentTimeMillis() + "\n"
                    + "}";

            boolean created = createExternalTestConfigFile(testConfigJson);
            if (created) {
                log("已创建测试配置文件，版本: V999");
                log("正在重启服务以触发版本检测...");

                // 重启服务触发重新检测
                restartConfigToolService(() -> {
                    // 服务重启完成后检查结果
                    try {
                        String newVersion = mConfigManager.getConfigValue("configVersion");
                        String testField = mConfigManager.getConfigValue("testField");

                        log("\n=== 版本比对结果 ===");
                        log("重新加载后的版本: " + newVersion);
                        log("测试字段值: " + testField);

                        // 检查是否成功更新到V999
                        String cleanVersion = newVersion != null ? newVersion.replace("\"", "") : "";
                        boolean versionUpdated = "V999".equals(cleanVersion);
                        boolean testFieldExists = testField != null
                                && !testField.contains("KEY_NOT_FOUND");

                        if (versionUpdated
                                && testFieldExists) {
                            log("\n🎉 版本比对测试成功！");
                            log("✓ 配置版本已更新为 V999");
                            log("✓ 测试字段已正确加载");
                        } else {
                            log("\n❌ 版本比对测试失败");
                            log("版本更新: " + versionUpdated + ", 字段存在: " + testFieldExists);
                        }
                    } catch (Exception exception) {
                        log("获取配置失败: " + exception.getMessage());
                    }
                });
            } else {
                log("创建测试配置文件失败");
                return;
            }

        } catch (Exception exception) {
            log("版本比对测试失败: " + exception.getMessage());
        }
    }

    /**
     * 测试加密重试机制.
     */
    private void testEncryptionRetry() {
        log("\n=== 测试加密重试机制 ===");

        try {
            // 创建一个大的配置文件来测试加密
            String testConfig = "{\n"
                    + "  \"configVersion\": \"V001\",\n"
                    + "  \"testRetry\": \"加密重试测试\",\n"
                    + "  \"largeData\": \"" + createRepeatedString("x", DEFAULT_MAX_DELETE_SIZE_MB) + "\",\n"
                    + "  \"timestamp\": " + System.currentTimeMillis() + "\n"
                    + "}";

            log("开始测试加密重试机制...");
            log("配置大小: " + testConfig.length() + " 字符");

            // 保存配置，这会触发加密过程
            mConfigManager.saveConfig(testConfig);
            log("配置保存请求已发送");
            log("请查看日志，观察是否有重试机制被触发");

        } catch (Exception exception) {
            log("加密重试测试失败: " + exception.getMessage());
        }
    }

    /**
     * 测试动态SD卡检测.
     */
    private void testDynamicSdCard() {
        log("\n=== 测试动态SD卡检测 ===");

        try {
            // 多次调用SD卡检测，验证动态检测功能
            for (int i = 1; i <= INT_3; i++) {
                log("第 " + i + " 次SD卡检测:");
                String sdCardPath = Constants.getExternalConfigPath();
                if (sdCardPath != null) {
                    log("  检测到SD卡路径: " + sdCardPath);
                    File sdCardDir = new File(sdCardPath);
                    log("  目录存在: " + sdCardDir.exists());
                    log("  可写入: " + sdCardDir.canWrite());
                } else {
                    log("  未检测到SD卡");
                }

                // 间隔1秒
                try {
                    Thread.sleep(Constants.ONE_SECOND_IN_MILLISECONDS);
                } catch (InterruptedException exception) {
                    Thread.currentThread().interrupt();
                }
            }

            log("动态SD卡检测测试完成");
            log("请插拔SD卡后再次测试，验证热插拔检测功能");

        } catch (Exception exception) {
            log("动态SD卡检测测试失败: " + exception.getMessage());
        }
    }

    /**
     * 测试EventBroker错误发送.
     */
    private void testEventBroker() {
        log("\n=== 测试EventBroker错误发送 ===");

        try {
            log("模拟SD卡不可用情况...");

            // 这个测试需要通过查看日志来验证
            // 因为EventBroker的错误发送是在ConfigToolService内部进行的
            log("请查看logcat日志，搜索以下关键词:");
            log("  - 'EventBroker bind attempt result'");
            log("  - 'SD card error event sent successfully'");
            log("  - 'External SD card not available, sending error event'");

            // 尝试获取一个配置来触发服务活动
            String version = mConfigManager.getConfigValue("configVersion");
            log("当前配置版本: " + version);
            log("如果SD卡不可用，应该会在日志中看到错误事件发送");

        } catch (Exception exception) {
            log("EventBroker测试失败: " + exception.getMessage());
        }
    }

    /**
     * 测试真实SD卡配置同步功能.
     */
    private void testExternalVersionCheck() {
        log("\n=== 测试真实SD卡配置同步功能 ===");

        try {
            // 获取当前版本和配置
            String currentVersion = mConfigManager.getConfigValue("configVersion");
            log("当前内部配置版本: " + currentVersion);

            // 第一步：从assets读取完整配置并修改
            log("\n步骤1: 从assets读取完整配置模板");
            String assetsConfig = readConfigFromAssets();
            if (assetsConfig == null) {
                log("✗ 无法读取assets配置文件");
                return;
            }
            log("✓ 成功读取assets配置，长度: " + assetsConfig.length() + " 字符");

            // 第二步：修改配置内容
            log("\n步骤2: 修改配置内容");
            log("✓ 配置修改完成，新版本: V888");
            log("✓ 修改了以下配置项:");
            log("  - configVersion: " + currentVersion + " → V888");
            log("  - eventRectime: 修改为 180 秒");
            log("  - audioPlay: 修改为 false");
            log("  - 添加测试字段: testSyncTime");

            // 第三步：写入SD卡
            log("\n步骤3: 将修改后的配置写入SD卡");
            String modifiedConfig = modifyConfigForTest(assetsConfig, currentVersion);
            if (createExternalTestConfigFile(modifiedConfig)) {
                log("✓ 配置文件已成功写入SD卡");
            } else {
                log("✗ 写入SD卡失败");
                return;
            }

            // 第四步：重启ConfigTool服务触发同步
            log("\n步骤4: 重启ConfigTool服务触发同步");
            log("正在重启服务...");

            restartConfigToolService(() -> {
                // 第五步：等待同步完成后检查结果
                log("\n步骤5: 检查同步结果");
                new Handler().postDelayed(() -> {
                    checkSyncResults();
                }, Constants.THREE_SECONDS_IN_MILLISECONDS); // 等待3秒让同步完成
            });

        } catch (Exception exception) {
            log("SD卡配置同步测试失败: " + exception.getMessage());
            exception.printStackTrace();
        }
    }

    /**
     * 从assets读取配置文件.
     */
    private String readConfigFromAssets() {
        try {
            InputStream inputStream = getAssets().open("config.json");
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            return new String(buffer, "UTF-8");
        } catch (Exception exception) {
            log("读取assets配置失败: " + exception.getMessage());
            return null;
        }
    }

    /**
     * 修改配置内容用于测试.
     */
    private String modifyConfigForTest(String originalConfig, String currentVersion) {
        try {
            // 解析JSON
            JSONObject config = new JSONObject(originalConfig);

            // 修改版本号
            config.put("configVersion", "V888");

            // 修改一些配置项
            config.put("eventRectime", Constants.TEST_EVENT_RECORDING_TIME_SECONDS);  // 修改事件录制时间
            config.put("audioPlay", false);   // 修改音频播放设置

            // 添加测试字段
            config.put("testSyncTime", System.currentTimeMillis());
            config.put("testSyncSource", "SD_CARD_SYNC_TEST");

            return config.toString(2); // 格式化输出
        } catch (Exception exception) {
            log("修改配置失败: " + exception.getMessage());
            return originalConfig;
        }
    }

    /**
     * 重启ConfigTool服务.
     */
    private void restartConfigToolService(Runnable onComplete) {
        try {
            // 停止服务
            Intent stopIntent = new Intent(this, ConfigToolService.class);
            stopService(stopIntent);
            log("✓ ConfigTool服务已停止");

            // 等待1秒后重新启动
            new Handler().postDelayed(() -> {
                try {
                    Intent startIntent = new Intent(this, ConfigToolService.class);
                    startService(startIntent);
                    log("✓ ConfigTool服务已重新启动");

                    // 重新绑定服务
                    new Handler().postDelayed(() -> {
                        bindConfigToolService();
                        if (onComplete != null) {
                            onComplete.run();
                        }
                    }, Constants.ONE_SECOND_IN_MILLISECONDS);

                } catch (Exception exception) {
                    log("重启服务失败: " + exception.getMessage());
                }
            }, Constants.ONE_SECOND_IN_MILLISECONDS);

        } catch (Exception exception) {
            log("停止服务失败: " + exception.getMessage());
        }
    }

    /**
     * 绑定ConfigTool服务.
     */
    private void bindConfigToolService() {
        try {
            if (!mServiceBound) {
                mConfigManager.bindService(this);
                log("正在重新绑定ConfigTool服务...");
            }
        } catch (Exception exception) {
            log("绑定服务失败: " + exception.getMessage());
        }
    }

    /**
     * 检查同步结果.
     */
    private void checkSyncResults() {
        try {
            log("正在检查同步结果...");

            String newVersion = mConfigManager.getConfigValue("configVersion");
            log("配置版本: " + newVersion);
            String eventRectime = mConfigManager.getConfigValue("eventRectime");
            log("事件录制时间: " + eventRectime);
            String audioPlay = mConfigManager.getConfigValue("audioPlay");
            log("音频播放: " + audioPlay);
            String testSyncTime = mConfigManager.getConfigValue("testSyncTime");
            log("测试同步时间: " + testSyncTime);
            String testSyncSource = mConfigManager.getConfigValue("testSyncSource");
            log("测试同步源: " + testSyncSource);

            // Remove quotes from JSON string values for comparison
            String cleanVersion = newVersion != null ? newVersion.replace("\"", "") : "";
            String cleanTestSource = testSyncSource != null ? testSyncSource.replace("\"", "") : "";

            boolean syncSuccess = "V888".equals(cleanVersion)
                                && String.valueOf(Constants.TEST_EVENT_RECORDING_TIME_SECONDS).equals(eventRectime)
                                && "false".equals(audioPlay)
                                && testSyncTime != null
                                && "SD_CARD_SYNC_TEST".equals(cleanTestSource);

            if (syncSuccess) {
                log("\n🎉 SD卡配置同步测试成功！");
                log("✓ 配置版本已更新为 V888");
                log("✓ 配置项已正确同步");
                log("✓ 测试字段已添加");
            } else {
                log("\n❌ SD卡配置同步测试失败");
                log("可能的原因:");
                log("- SD卡未正确挂载");
                log("- 配置文件格式错误");
                log("- 服务重启未生效");
                log("- 版本比较逻辑问题");
            }

        } catch (Exception exception) {
            log("检查同步结果失败: " + exception.getMessage());
        }
    }
}