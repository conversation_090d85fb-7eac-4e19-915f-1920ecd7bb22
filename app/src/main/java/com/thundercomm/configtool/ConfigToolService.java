/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.ParcelFileDescriptor;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.thundercomm.configtool.api.IConfigToolServiceManager;
import com.thundercomm.configtool.utils.Constants;
import com.thundercomm.configtool.utils.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import vendor.thundercomm.eventbroker.EventBody;
import vendor.thundercomm.eventbroker.EventEntry;
import vendor.thundercomm.eventbroker.EventHeader;
import vendor.thundercomm.eventbroker.IEventBroker;
import com.thundercomm.crown.fileencrypt.api.FileEncryptServiceManager;
import com.thundercomm.crown.fileencrypt.api.IFileEncryptServiceConnectCallback;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.InputStream;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.io.ByteArrayOutputStream;

/**
 * Configuration Tool Service.
 * Provides encrypted storage and decrypted reading of configuration files.
 */
public class ConfigToolService extends Service {
    private static final String TAG = "ConfigToolService";

    // Encryption/Decryption operation flags
    private static final int FLAG_ENCRYPT = 0;
    private static final int FLAG_DECRYPT = 1;

    // Cache for storing decrypted configuration data
    private Map<String, JsonElement> mConfigCache;

    // File encryption service binding flag
    private boolean mEncryptServiceBound = false;

    // File encryption service manager
    private FileEncryptServiceManager mFileEncryptManager;

    private static final int INT_3 = 3;
    private static final int INT_5000 = 5000;

    // Event constants
    private static final byte RUNTIME_ERROR_CATEGORY_ID = (byte) 0x03;
    private static final int SD_CARD_HARDWARE_ID = 9;
    // Handler for delayed backup operations
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mBackupRunnable;

    // FileEncrypt service connect callback
    private IFileEncryptServiceConnectCallback mFileEncryptConnectCallback = new IFileEncryptServiceConnectCallback() {
        @Override
        public void onServiceConnected() {
            Logger.begin(TAG, "FileEncrypt service connected");
            Logger.i(TAG, "FileEncryptService connected successfully");
            mEncryptServiceBound = true;

            // Reset service retry flag
            mServiceRetryFailed = false;

            // Execute initialization in a separate thread to avoid blocking the main thread
            new Thread(() -> {
                // Only initialize the configuration when mConfigCache is empty to avoid repeated checks
                if (mConfigCache == null || mConfigCache.isEmpty()) {
                    Logger.d(TAG, "Cache is empty, checking and initializing config");
                    checkAndInitializeConfig();
                } else {
                    Logger.d(TAG, "Cache already initialized, skip checking config");
                }
            }).start();

            Logger.end(TAG, "FileEncrypt service connected", true);
        }

        @Override
        public void onServiceDisconnected() {
            Logger.begin(TAG, "FileEncrypt service disconnected");
            mEncryptServiceBound = false;
            Logger.w(TAG, "FileEncryptService disconnected");
            Logger.end(TAG, "FileEncrypt service disconnected", true);
        }
    };

    // AIDL service interface implementation
    private final IConfigToolServiceManager.Stub mBinder = new IConfigToolServiceManager.Stub() {
        @Override
        public boolean saveConfig(String jsonConfig) {
            Logger.methodIn(TAG, "saveConfig");
            Logger.param(TAG, "jsonConfig", jsonConfig != null ? "length: " + jsonConfig.length() : "null");
            boolean result = ConfigToolService.this.saveConfig(jsonConfig);
            Logger.methodOut(TAG, "saveConfig");
            return result;
        }

        @Override
        public String getConfigValue(String key) {
            Logger.methodIn(TAG, "getConfigValue");
            Logger.param(TAG, "key", key);
            String result = ConfigToolService.this.getConfigValue(key);
            Logger.param(TAG, "result", result != null ? "length: " + result.length() : "null");
            Logger.methodOut(TAG, "getConfigValue");
            return result;
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        Logger.begin(TAG, "onCreate");
        Logger.i(TAG, "ConfigToolService onCreate");

        // Initialize Constants with context for SD card detection
        Constants.initialize(this);
        Logger.d(TAG, "Constants initialized with context");

        // Initialize configuration cache
        mConfigCache = new HashMap<>();
        Logger.d(TAG, "Configuration cache initialization complete");

        // Reset service retry flag
        mServiceRetryFailed = false;
        Logger.d(TAG, "Reset service retry flag");

        // Initialize FileEncryptServiceManager
        Logger.d(TAG, "Initializing FileEncryptServiceManager");
        mFileEncryptManager = FileEncryptServiceManager.getInstance();
        mFileEncryptManager.setServiceConnectCallback(mFileEncryptConnectCallback);

        // Bind file encryption service
        Logger.d(TAG, "Start binding file encryption service using FileEncryptServiceManager");
        mFileEncryptManager.bindService(this);

        // Try to bind EventBroker service with delay to ensure system is ready
        scheduleEventBrokerBinding();

        // Actively perform configuration check at service initialization
        // We don't call checkAndInitializeConfig here, as we need to wait for the encryption service to connect
        // The encryption service connection callback will call checkAndInitializeConfig

        Logger.end(TAG, "onCreate", true);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Logger.begin(TAG, "onStartCommand");
        Logger.d(TAG, "ConfigToolService onStartCommand, startId: " + startId);

        // Reset failure flag each time the service starts
        mServiceRetryFailed = false;

        // Execute initialization in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            // If encryption service is already connected, directly check and initialize configuration
            if (mEncryptServiceBound) {
                Logger.d(TAG, "Encryption service already bound, checking and initializing config");
                checkAndInitializeConfig();
            } else if (mFileEncryptManager != null) {
                Logger.d(TAG, "Encryption service not bound, attempting to bind");
                mFileEncryptManager.bindService(ConfigToolService.this);
                // The connection callback will call checkAndInitializeConfig after successful connection
            }
        }).start();

        Logger.end(TAG, "onStartCommand", true);
        return START_STICKY;  // If the service is killed by the system, it will attempt to restart
    }

    @Override
    public void onDestroy() {
        Logger.begin(TAG, "onDestroy");
        super.onDestroy();

        // Clean up backup tasks
        if (mHandler != null && mBackupRunnable != null) {
            mHandler.removeCallbacks(mBackupRunnable);
            Logger.d(TAG, "Backup tasks cleaned up");
        }

        // Clean up retry handlers
        if (mRetryHandler != null) {
            mRetryHandler.removeCallbacksAndMessages(null);
            Logger.d(TAG, "Retry handler cleaned up");
        }

        // Reset unified retry flags
        mIsRetryingSdCard = false;
        mSdCardRetryForJson = false;
        mSdCardRetryForBin = false;

        // Unbind file encryption service
        if (mEncryptServiceBound && mFileEncryptManager != null) {
            Logger.d(TAG, "Unbinding file encryption service");
            mFileEncryptManager.unbindService(this);
            mEncryptServiceBound = false;
        } else {
            Logger.d(TAG, "File encryption service not bound, no need to unbind");
        }

        // Unbind EventBroker service
        if (mEventBrokerBound && mEventBrokerConnection != null) {
            Logger.d(TAG, "Unbinding EventBroker service");
            try {
                unbindService(mEventBrokerConnection);
            } catch (Exception ex) {
                Logger.w(TAG, "Error unbinding EventBroker service", ex);
            }
            mEventBrokerBound = false;
            mEventBroker = null;
        } else {
            Logger.d(TAG, "EventBroker service not bound, no need to unbind");
        }

        Logger.end(TAG, "onDestroy", true);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        Logger.d(TAG, "ConfigToolService onBind: " + intent);
        return mBinder;
    }

    // Add flag to indicate whether a save operation is currently being executed
    private volatile boolean mIsSaving = false;

    // Retry mechanism constants
    private static final int ENCRYPTION_RETRY_MAX_ATTEMPTS = 5;
    private static final int ENCRYPTION_RETRY_DELAY_MS = Constants.TEN_SECONDS_IN_MILLISECONDS; // 10 seconds

    // EventBroker related
    private IEventBroker mEventBroker;
    private boolean mEventBrokerBound = false;
    private ServiceConnection mEventBrokerConnection;

    // EventBroker retry mechanism
    private volatile boolean mIsRetryingEventBroker = false;
    private int mEventBrokerRetryCount = 0;
    private static final int MAX_EVENTBROKER_RETRIES = 5;
    private static final int EVENTBROKER_INITIAL_DELAY_MS = Constants.ONE_SECOND_IN_MILLISECONDS; // 5秒初始延时
    private static final int EVENTBROKER_RETRY_DELAY_MS = Constants.TWO_SECONDS_IN_MILLISECONDS; // 3秒重试间隔



    // Unified SD card retry mechanism variables
    private volatile boolean mIsRetryingSdCard = false;
    private volatile boolean mSdCardRetryForJson = false;
    private volatile boolean mSdCardRetryForBin = false;
    private Handler mRetryHandler = new Handler(Looper.getMainLooper());
    private static final int MAX_SD_RETRIES = 3;
    private static final int SD_RETRY_DELAY_MS = Constants.FIVE_SECONDS_IN_MILLISECONDS;

    /**
     * Save configuration.
     *
     * @param jsonConfig JSON configuration string
     * @return Returns true if saved successfully, otherwise returns false
     */
    private boolean saveConfig(String jsonConfig) {
        return saveConfig(jsonConfig, false);
    }

    /**
     * Save configuration with retry option for internal loading scenarios.
     *
     * @param jsonConfig JSON configuration string
     * @param enableRetry Whether to enable retry mechanism when bin validation fails
     * @return Returns true if saved successfully, otherwise returns false
     */
    private boolean saveConfig(String jsonConfig, boolean enableRetry) {
        // If already saving, avoid duplicate calls
        if (mIsSaving) {
            Logger.w(TAG, "Already in saving process, skip duplicate save");
            return false;
        }

        Logger.begin(TAG, "Save configuration");
        Logger.param(TAG, "jsonConfig length", jsonConfig != null ? jsonConfig.length() : 0);

        if (jsonConfig == null || jsonConfig.isEmpty()) {
            Logger.e(TAG, "Invalid JSON configuration: empty or null");
            Logger.end(TAG, "Save configuration", false);
            return false;
        }

        try {
            // Set saving flag
            mIsSaving = true;

            // Check JSON format
            if (!isValidJson(jsonConfig)) {
                Logger.e(TAG, "Invalid JSON configuration format");
                Logger.end(TAG, "Save configuration", false);
                return false;
            }

            // Reset retry flag, give encryption service a new chance
            mServiceRetryFailed = false;

            // Check encryption service connection status
            if (!mEncryptServiceBound || mFileEncryptManager == null) {
                Logger.d(TAG, "Encryption service not bound, attempting to bind");
                tryBindEncryptService();

                // Give encryption service a moment to connect
                int retryCount = 0;
                while (!mEncryptServiceBound && retryCount < Constants.CONNECTION_RETRY_LIMIT) {
                    try {
                        Logger.d(TAG, "Waiting for encryption service to connect, attempt " + (retryCount + 1));
                        Thread.sleep(Constants.CONNECTION_RETRY_DELAY_MS);
                        retryCount++;
                    } catch (InterruptedException ex) {
                        Thread.currentThread().interrupt();
                        Logger.w(TAG, "Interrupted while waiting for encryption service to connect");
                        break;
                    }
                }

                // If still not bound after retries, return failure
                if (!mEncryptServiceBound) {
                    Logger.e(TAG, "Failed to bind encryption service after " + retryCount + " attempts");
                    mIsSaving = false;
                    Logger.end(TAG, "Save configuration", false);
                    return false;
                }
            }

            // Parse JSON configuration
            JsonObject inputJsonObject = JsonParser.parseString(jsonConfig).getAsJsonObject();

            // Compare new and old configurations to identify modified and added fields
            JsonObject modifiedFields = new JsonObject();
            JsonObject addedFields = new JsonObject();

            for (Map.Entry<String, JsonElement> entry : inputJsonObject.entrySet()) {
                String key = entry.getKey();
                JsonElement newValue = entry.getValue();

                // Only consider as modified if values are not equal
                if (mConfigCache.containsKey(key)) {
                    JsonElement oldValue = mConfigCache.get(key);
                    // Use equals for strict comparison to ensure identical content is not considered as modified
                    if (!oldValue.equals(newValue)) {
                        modifiedFields.add(key, newValue);
                    } else {
                        // Values are identical, not a modified item
                    }
                } else {
                    // New field added
                    addedFields.add(key, newValue);
                }
            }

            // Modify merge logic here, first create an object containing all existing configurations
            JsonObject mergedJsonObject = new JsonObject();

            // First add all items from cache
            for (Map.Entry<String, JsonElement> entry : mConfigCache.entrySet()) {
                mergedJsonObject.add(entry.getKey(), entry.getValue());
            }

            // Then add or update all items from input
            for (Map.Entry<String, JsonElement> entry : inputJsonObject.entrySet()) {
                mergedJsonObject.add(entry.getKey(), entry.getValue());
            }

            Logger.d(TAG, "Merged configuration has " + mergedJsonObject.size() + " items");

            // Encrypt and save configuration
            boolean encryptResult = encryptAndSaveConfig(mergedJsonObject);
            if (!encryptResult) {
                Logger.e(TAG, "Failed to encrypt and save configuration");
                Logger.end(TAG, "Save configuration", false);
                return false;
            }

            // Validate the encrypted configuration by trying to decrypt and check configVersion
            Logger.d(TAG, "Validating encrypted configuration integrity");
            if (!validateEncryptedConfigFile()) {
                Logger.e(TAG, "Encrypted configuration validation failed");

                if (enableRetry) {
                    // Internal loading: start retry mechanism
                    Logger.w(TAG, "Starting retry mechanism for internal loading");
                    retryEncryptionWithDelay(jsonConfig);
                }
                // Both external and internal calls return false immediately
                Logger.end(TAG, "Save configuration", false);
                return false;
            }

            // Update cache with merged configuration
            boolean cacheUpdateResult = updateCacheAndNotify(mergedJsonObject, modifiedFields, addedFields);
            if (!cacheUpdateResult) {
                Logger.w(TAG, "Failed to update cache or notify changes");
            }

            // Send broadcast to notify configuration changes
            if (modifiedFields.size() > 0 || addedFields.size() > 0) {
                Logger.d(TAG, "Broadcasting configuration changes");
                sendConfigChangedBroadcast(modifiedFields, addedFields);
            }

            // Schedule external backup with delay and retry
            Logger.i(TAG, "Initiating Handler-based external backup with delay and retry");
            scheduleExternalBackupWithRetry(false);
            Logger.end(TAG, "Save configuration", true);
            return true;
        } catch (IOException ioEx) {
            Logger.e(TAG, "Save configuration I/O error", ioEx);
            return false;
        } catch (Exception ex) {
            Logger.e(TAG, "Unexpected error during save configuration", ex);
            return false;
        } finally {
            // Always reset saving flag when done
            mIsSaving = false;
        }
    }

    /**
     * Retry encryption with delay for internal loading scenarios.
     * This method will retry encryption 5 times with 10-second intervals.
     *
     * @param jsonConfig JSON configuration string to retry encryption
     */
    private void retryEncryptionWithDelay(String jsonConfig) {
        new Thread(() -> {
            Logger.d(TAG, "Starting encryption retry process");

            for (int attempt = 1; attempt <= ENCRYPTION_RETRY_MAX_ATTEMPTS; attempt++) {
                try {
                    Logger.d(TAG, "Waiting " + (ENCRYPTION_RETRY_DELAY_MS / Constants.ONE_SECOND_IN_MILLISECONDS)
                            + " seconds before retry attempt " + attempt);
                    Thread.sleep(ENCRYPTION_RETRY_DELAY_MS);

                    Logger.d(TAG, "Encryption retry attempt " + attempt + "/" + ENCRYPTION_RETRY_MAX_ATTEMPTS);

                    // Retry with enableRetry=false to avoid recursive retry
                    if (saveConfig(jsonConfig, false)) {
                        Logger.i(TAG, "Encryption retry successful on attempt " + attempt);
                        return; // Success, exit retry loop
                    } else {
                        Logger.w(TAG, "Encryption retry failed on attempt " + attempt);
                    }

                } catch (InterruptedException es) {
                    Thread.currentThread().interrupt();
                    Logger.w(TAG, "Encryption retry interrupted");
                    return;
                } catch (Exception ex) {
                    Logger.e(TAG, "Exception during encryption retry attempt " + attempt, ex);
                }
            }

            Logger.e(TAG, "All encryption retry attempts failed after " + ENCRYPTION_RETRY_MAX_ATTEMPTS + " attempts");
        }).start();
    }

    /**
     * Check and initialize configuration.
     * If the encrypted configuration file does not exist in the application data directory,
     * read the configuration from the SD card and save it encrypted.
     */
    private void checkAndInitializeConfig() {
        // If currently saving, skip initialization
        if (mIsSaving) {
            Logger.d(TAG, "Currently saving, skip initialization");
            return;
        }

        Logger.begin(TAG, "Check and initialize configuration");

        // 1. First check the internal storage encrypted configuration file
        File encryptedConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
        Logger.d(TAG, "1. Checking internal encrypted config file: " + encryptedConfigFile.getAbsolutePath());

        if (encryptedConfigFile.exists()) {
            Logger.i(TAG, "Internal encrypted config file exists, size: " + encryptedConfigFile.length() + " bytes");
            // Load existing configuration to cache
            boolean loadResult = loadConfigToCache();

            if (loadResult) {
                Logger.d(TAG, "Successfully loaded configuration from internal encrypted file");

                // Check external SD card for newer configuration version with retry
                Logger.d(TAG, "Checking external SD card for newer configuration version");
                if (loadFromExternalJsonWithRetry()) {
                    Logger.i(TAG, "Found and loaded newer configuration from external SD card");
                    Logger.end(TAG, "Check and initialize configuration", true);
                    return;
                }

                // Check if external backup exists, if not, create one (with retry)
                if (!isExternalBinFileExistsWithRetry()) {
                    Logger.i(TAG, "External backup does not exist, scheduling backup creation");
                    scheduleExternalBackupWithRetry(false);
                } else {
                    Logger.d(TAG, "External backup already exists, no need to create");
                }

                Logger.end(TAG, "Check and initialize configuration", true);
                return;
            } else {
                Logger.w(TAG, "Internal encrypted config file exists but failed to load");
                // Failed to decrypt internal file, try external backup
            }
        } else {
            Logger.i(TAG, "No encrypted config file in internal storage");
        }

        // 2. Check encrypted backup file in external storage
        Logger.d(TAG, "2. Checking external encrypted backup file");
        if (tryRestoreFromBackup(encryptedConfigFile)) {
            Logger.i(TAG, "Successfully restored from external encrypted backup");
            Logger.end(TAG, "Check and initialize configuration", true);
            return;
        }

        // 3. Check plaintext configuration file in internal storage
        Logger.d(TAG, "3. Checking internal JSON config file");
        File internalJsonFile = new File(getFilesDir(), Constants.EXTERNAL_CONFIG_FILENAME);
        if (internalJsonFile.exists() 
            && internalJsonFile.canRead()) {
            Logger.i(TAG, "Found internal JSON config file, size: " + internalJsonFile.length() + " bytes");
            try {
                // Read plaintext configuration content
                String configJson = readFileToString(internalJsonFile);
                if (configJson != null 
                    && !configJson.isEmpty() 
                    && isValidJson(configJson)) {
                    Logger.d(TAG, "Valid JSON configuration found in internal storage");

                    // Parse to cache
                    if (parseJsonToCache(configJson)) {
                        Logger.i(TAG, "Successfully loaded configuration from internal JSON file");

                        // Save encrypted
                        if (mEncryptServiceBound && checkEncryptionServiceAvailable()) {
                            Logger.d(TAG, "Attempting to save encrypted version of internal JSON configuration");
                            boolean saveResult = saveConfig(configJson);
                            if (saveResult) {
                                // Delete plaintext file after successful save
                                if (internalJsonFile.delete()) {
                                    Logger.i(TAG, "Deleted internal JSON file after encryption");
                                } else {
                                    Logger.w(TAG, "Failed to delete internal JSON file after encryption");
                                }
                            }
                        }

                        Logger.end(TAG, "Check and initialize configuration", true);
                        return;
                    }
                }
            } catch (IOException ioEx) {
                Logger.e(TAG, "Failed to read internal JSON configuration file", ioEx);
            }
        }

        // 4. Finally try to read external plaintext configuration file
        Logger.d(TAG, "4. Checking external JSON config file");
        if (tryLoadFromExternalJson()) {
            Logger.i(TAG, "Successfully loaded from external JSON file");
            Logger.end(TAG, "Check and initialize configuration", true);
            return;
        }

        // If no configuration file is found, do nothing, will not retry
        Logger.w(TAG, "No configuration file found in any location, using empty configuration");
        // Initialize empty config cache to avoid null pointer exceptions in subsequent access
        if (mConfigCache == null) {
            mConfigCache = new HashMap<>();
        }
        Logger.end(TAG, "Check and initialize configuration", false);
    }



    /**
     * Try to restore configuration from external backup file.
     *
     * @param encryptedConfigFile Internal encrypted configuration file
     * @return Returns true if restored successfully, otherwise returns false
     */
    private boolean tryRestoreFromBackup(File encryptedConfigFile) {
        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.w(TAG, "External SD card not available, cannot restore from backup");
            return false;
        }

        File externalEncryptedFile = new File(externalConfigPath, Constants.ENCRYPTED_CONFIG_FILENAME);
        Logger.d(TAG, "External encrypted config file path: " + externalEncryptedFile.getAbsolutePath());

        if (!externalEncryptedFile.exists()
            || !externalEncryptedFile.canRead()) {
            Logger.d(TAG, "No external encrypted config file or cannot read");
            return false;
        }

        Logger.i(TAG, "Found encrypted backup file in external storage, size: "
                + externalEncryptedFile.length() + " bytes");
        try {
            // Create internal storage directory (if it doesn't exist)
            File internalDir = getFilesDir();
            if (!internalDir.exists()) {
                internalDir.mkdirs();
            }

            // Copy backup file to internal storage
            copyFile(externalEncryptedFile, encryptedConfigFile);
            Logger.i(TAG, "Restored encrypted config file from backup");

            // Load restored configuration to cache
            boolean loadResult = loadConfigToCache();
            Logger.d(TAG, "Load configuration from restored backup result: " + loadResult);

            return loadResult;
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to restore encrypted config from backup", ioEx);
            return false;
        }
    }

    /**
     * Create directory if it doesn't exist.
     *
     * @param directory Directory to check and create
     * @return Returns true if directory exists or was created successfully
     */
    private boolean ensureDirectoryExists(File directory) {
        if (!directory.exists()) {
            boolean result = directory.mkdirs();
            Logger.d(TAG, "Create directory result for " + directory.getPath() + ": " + result);
            return result;
        }
        return true;
    }

    /**
     * Write string content to file.
     *
     * @param content Content to write
     * @param file Target file
     * @return Returns true if write was successful
     */
    private boolean writeStringToFile(String content, File file) {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
            fos.write(bytes);
            Logger.d(TAG, "Write " + bytes.length + " bytes to file: " + file.getAbsolutePath());
            return true;
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to write to file: " + file.getAbsolutePath(), ioEx);
            return false;
        }
    }

    /**
     * Load configuration file from application assets directory.
     *
     * @return Configuration file content, or null if failed
     * @throws IOException If an I/O error occurs
     */
    private String loadConfigFromAssets() throws IOException {
        Logger.begin(TAG, "loadConfigFromAssets");

        try {
            // Open configuration file from assets directory
            try (java.io.InputStream is = getAssets().open("config.json")) {
                // Read file content
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[Constants.DEFAULT_BUFFER_SIZE];
                int length;
                while ((length = is.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                String configContent = result.toString(StandardCharsets.UTF_8.name());

                Logger.end(TAG, "loadConfigFromAssets", true);
                return configContent;
            }
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to read configuration file from assets directory", ioEx);
            Logger.end(TAG, "loadConfigFromAssets", false);
            throw ioEx;
        }
    }

    /**
     * Try to load configuration from assets directory.
     * 
     * @return true if successfully loaded and parsed to cache, false otherwise
     */
    private boolean tryLoadFromAssets() {
        Logger.begin(TAG, "tryLoadFromAssets");

        try {
            Logger.i(TAG, "Trying to load default configuration file from assets directory");
            String assetsConfigJson = loadConfigFromAssets();
            if (assetsConfigJson != null && !assetsConfigJson.isEmpty()) {
                // Check JSON format
                if (!isValidJson(assetsConfigJson)) {
                    Logger.e(TAG, "Invalid JSON format in assets directory configuration file");
                    Logger.end(TAG, "tryLoadFromAssets", false);
                    return false;
                }

                // Parse to cache
                boolean parseResult = parseJsonToCache(assetsConfigJson);
                if (parseResult) {
                    Logger.i(TAG, "Successfully loaded default configuration from assets directory");

                    // Reset encryption service failure flag
                    mServiceRetryFailed = false;

                    // Try to encrypt and save with retry mechanism for internal loading
                    try {
                        if (mEncryptServiceBound && checkEncryptionServiceAvailable()) {
                            Logger.d(TAG, "Attempting to encrypt default configuration from assets");
                            boolean saveResult = saveConfig(assetsConfigJson, true);
                            if (saveResult) {
                                Logger.i(TAG, "Successfully encrypted and saved default configuration from assets");
                            }
                        }
                    } catch (Exception ex) {
                        Logger.w(TAG, "Failed to encrypt default configuration from assets", ex);
                    }

                    Logger.end(TAG, "tryLoadFromAssets", true);
                    return true;
                }
            } else {
                Logger.w(TAG, "Configuration file in assets directory is empty or does not exist");
            }
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to read configuration file from assets directory", ioEx);
        }

        Logger.end(TAG, "tryLoadFromAssets", false);
        return false;
    }

    /**
     * Try to load configuration from external JSON file.
     *
     * @return Returns true if loaded successfully, otherwise returns false
     */
    private boolean tryLoadFromExternalJson() {
        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.w(TAG, "External SD card not available, sending error event");
            sendSdCardNotInsertedError();

            // SD card not available, try to load from assets instead
            Logger.d(TAG, "Trying to load from assets since SD card is not available");
            if (tryLoadFromAssets()) {
                return true;
            }

            // If loading from assets fails, try using the application's external storage directory
            if (loadFromAppExternalStorage()) {
                return true;
            }

            return false;
        }

        File externalConfigFile = new File(externalConfigPath, Constants.EXTERNAL_CONFIG_FILENAME);
        Logger.d(TAG, "External config file path: " + externalConfigFile.getAbsolutePath());

        if (!externalConfigFile.exists()
            || !externalConfigFile.canRead()) {
            Logger.w(TAG, "External config file does not exist or cannot be read: "
                    + externalConfigFile.getAbsolutePath());

            // Try using the application's external storage directory as fallback
            File appExternalConfigFile = new File(getExternalFilesDir(null), Constants.EXTERNAL_CONFIG_FILENAME);
            Logger.d(TAG, "Trying to use app's external storage directory: "
                    + appExternalConfigFile.getAbsolutePath());

            if (appExternalConfigFile.exists() && appExternalConfigFile.canRead()) {
                Logger.i(TAG, "Found configuration file in app's external storage, size: "
                        + appExternalConfigFile.length() + " bytes");
                externalConfigFile = appExternalConfigFile;
            } else {
                // No external config found, return false without loading assets
                // This is used in fallback scenarios where assets loading is appropriate
                return false;
            }
        }

        Logger.i(TAG, "Loading configuration from external JSON file, file size: "
                + externalConfigFile.length() + " bytes");

        try {
            // Read plaintext configuration content
            String configJson = readFileToString(externalConfigFile);
            if (configJson == null || configJson.isEmpty()) {
                Logger.e(TAG, "External JSON configuration is empty");
                return false;
            }

            // Check JSON format
            if (!isValidJson(configJson)) {
                Logger.e(TAG, "Configuration JSON format is invalid");
                return false;
            }

            // Check version compatibility before processing
            if (!isExternalConfigVersionCompatible(configJson)) {
                Logger.w(TAG, "External config version is not higher than current version, skipping");
                return false;
            }

            // Parse to cache first to ensure configuration can be used normally even if encryption fails
            boolean parseResult = parseJsonToCache(configJson);
            if (!parseResult) {
                Logger.e(TAG, "Failed to parse external JSON configuration");
                return false;
            }

            Logger.i(TAG, "Successfully loaded configuration from external JSON file");

            // Reset encryption service failure flag
            mServiceRetryFailed = false;

            // Try to encrypt and save with retry mechanism for internal loading
            try {
                if (mEncryptServiceBound && checkEncryptionServiceAvailable()) {
                    Logger.d(TAG, "Attempting to encrypt external JSON configuration");
                    boolean saveResult = saveConfig(configJson, true); // Enable retry for internal loading
                    if (saveResult) {
                        // Only delete the plaintext file if the save is successful
                        Logger.d(TAG, "Encrypted save successful, deleting external JSON file");
                        deleteExternalJsonFile(externalConfigFile);
                    }
                } else {
                    Logger.w(TAG, "Encryption service not available, keeping external JSON file");
                }
            } catch (Exception ex) {
                Logger.w(TAG, "Failed to encrypt external JSON configuration", ex);
            }

            return true;
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to read external configuration file", ioEx);
            return false;
        }
    }

    /**
     * Load configuration from external JSON file with unified retry mechanism.
     * This method handles SD card mount delays by using a unified retry approach.
     */
    private boolean loadFromExternalJsonWithRetry() {
        Logger.d(TAG, "Request external JSON load with unified retry mechanism");

        // Try once immediately
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath != null) {
            Logger.i(TAG, "SD card available immediately for JSON, path: " + externalConfigPath);
            return loadFromExternalJson();
        }

        // SD card not available, request unified retry for JSON
        Logger.d(TAG, "SD card not available for JSON, requesting unified retry");
        requestUnifiedSdCardRetry(true, false);
        return false;
    }

    /**
     * Request unified SD card retry mechanism.
     * This prevents multiple parallel retry flows.
     */
    private void requestUnifiedSdCardRetry(boolean forJson, boolean forBin) {
        // Set retry flags
        if (forJson) {
            mSdCardRetryForJson = true;
        }
        if (forBin) {
            mSdCardRetryForBin = true;
        }

        // If already retrying, just update flags and return
        if (mIsRetryingSdCard) {
            Logger.d(TAG, "Unified SD card retry already in progress, updated flags (JSON:"
                    + mSdCardRetryForJson + ", Bin:" + mSdCardRetryForBin + ")");
            return;
        }

        // Start unified retry
        Logger.d(TAG, "Starting unified SD card retry (JSON:" + mSdCardRetryForJson + ", Bin:"
                + mSdCardRetryForBin + ")");
        mIsRetryingSdCard = true;
        scheduleUnifiedSdCardRetry(1);
    }

    /**
     * Schedule next unified SD card retry attempt.
     */
    private void scheduleUnifiedSdCardRetry(int attempt) {
        if (attempt > MAX_SD_RETRIES) {
            // All retries failed
            Logger.w(TAG, "Unified SD card retry failed after " + MAX_SD_RETRIES + " attempts");
            mIsRetryingSdCard = false;

            // Send error event only once
            if (mSdCardRetryForJson || mSdCardRetryForBin) {
                sendSdCardNotInsertedError();
            }

            // Handle bin file backup creation if needed
            if (mSdCardRetryForBin) {
                mHandler.post(() -> {
                    Logger.i(TAG, "External backup does not exist, scheduling backup creation");
                    scheduleExternalBackupWithRetry(false);
                });
            }

            // Reset flags
            mSdCardRetryForJson = false;
            mSdCardRetryForBin = false;
            return;
        }

        mRetryHandler.postDelayed(() -> {
            Logger.d(TAG, "Unified SD card check attempt " + attempt + "/" + MAX_SD_RETRIES
                    + " (JSON:" + mSdCardRetryForJson + ", Bin:" + mSdCardRetryForBin + ")");

            String externalConfigPath = Constants.getExternalConfigPath();
            if (externalConfigPath != null) {
                // Success, stop retrying
                Logger.i(TAG, "SD card available on unified attempt " + attempt + ", path: " + externalConfigPath);
                mIsRetryingSdCard = false;

                // Process requests on background thread
                new Thread(() -> {
                    if (mSdCardRetryForJson) {
                        if (loadFromExternalJson()) {
                            Logger.i(TAG, "Successfully loaded JSON configuration via unified retry");
                        }
                    }

                    if (mSdCardRetryForBin) {
                        if (isExternalBinFileExists()) {
                            Logger.d(TAG, "External backup already exists via unified retry");
                        } else {
                            mHandler.post(() -> {
                                Logger.i(TAG, "External backup does not exist, scheduling backup creation");
                                scheduleExternalBackupWithRetry(false);
                            });
                        }
                    }

                    // Reset flags
                    mSdCardRetryForJson = false;
                    mSdCardRetryForBin = false;
                }).start();
                return;
            }

            Logger.w(TAG, "SD card not available on unified attempt " + attempt + "/" + MAX_SD_RETRIES);

            // Schedule next retry
            scheduleUnifiedSdCardRetry(attempt + 1);

        }, SD_RETRY_DELAY_MS);
    }

    /**
     * Load configuration from external JSON file.
     */
    private boolean loadFromExternalJson() {
        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.w(TAG, "External SD card not available");
            return false;
        }

        File externalConfigFile = new File(externalConfigPath, Constants.EXTERNAL_CONFIG_FILENAME);
        if (!externalConfigFile.exists()
            || !externalConfigFile.canRead()) {
            Logger.w(TAG, "External config file does not exist or cannot be read: "
                    + externalConfigFile.getAbsolutePath());

            // External config not found, return false without loading assets
            // This allows the system to use existing internal configuration
            return false;
        }

        try {
            // Read plaintext configuration content
            String configJson = readFileToString(externalConfigFile);
            if (configJson == null || configJson.isEmpty() || !isValidJson(configJson)) {
                Logger.e(TAG, "External JSON configuration is invalid or empty");
                return false;
            }

            // Check version compatibility before processing
            if (!isExternalConfigVersionCompatible(configJson)) {
                Logger.w(TAG, "External config version is not higher than current version, skipping");
                return false;
            }

            // Parse to cache first to ensure configuration can be used normally even if encryption fails
            boolean parseResult = parseJsonToCache(configJson);
            if (!parseResult) {
                Logger.e(TAG, "Failed to parse external JSON configuration");
                return false;
            }

            Logger.i(TAG, "Successfully loaded configuration from external JSON file");

            // Reset encryption service failure flag
            mServiceRetryFailed = false;

            // Try to encrypt and save with retry mechanism for internal loading
            try {
                if (mEncryptServiceBound && checkEncryptionServiceAvailable()) {
                    Logger.d(TAG, "Attempting to encrypt external JSON configuration (loadFromExternalJson)");
                    boolean saveResult = saveConfig(configJson, true); // Enable retry for internal loading
                    if (saveResult) {
                        // Only delete the plaintext file if the save is successful
                        Logger.d(TAG, "Encrypted save successful, deleting external JSON file");
                        deleteExternalJsonFile(externalConfigFile);
                    }
                } else {
                    Logger.w(TAG, "Encryption service not available, keeping external JSON file");
                }
            } catch (Exception ex) {
                Logger.w(TAG, "Failed to encrypt external JSON configuration (loadFromExternalJson)", ex);
            }

            return true;
        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to read external configuration file", ioEx);
            return false;
        }
    }

    /**
     * Delete external JSON configuration file
     *
     * @param externalConfigFile External JSON configuration file
     */
    private void deleteExternalJsonFile(File externalConfigFile) {
        Logger.i(TAG, "Successfully saved config, deleting external JSON file: "
                + externalConfigFile.getAbsolutePath());

        // Ensure the file still exists before trying to delete it
        if (externalConfigFile.exists()) {
            try {
                // Ensure the file has write permission
                boolean canWrite = externalConfigFile.canWrite();
                Logger.d(TAG, "External JSON file canWrite: " + canWrite);

                if (!canWrite) {
                    boolean setWritableResult = externalConfigFile.setWritable(true);
                    Logger.d(TAG, "Set external JSON file writable: " + setWritableResult);
                }

                // Try to delete the file
                boolean deleteResult = externalConfigFile.delete();
                Logger.d(TAG, "External JSON config file delete result: " + deleteResult);

                if (!deleteResult) {
                    // If deletion fails, try using File.deleteOnExit()
                    externalConfigFile.deleteOnExit();
                    Logger.d(TAG, "Failed to delete immediately, scheduled deletion on exit");

                    // Or try overwriting with empty file
                    try (FileOutputStream fos = new FileOutputStream(externalConfigFile)) {
                        // Write empty content, set file size to 0
                        fos.write(new byte[0]);
                        fos.flush();
                        Logger.d(TAG, "Overwrote file with empty content");
                    } catch (IOException ioEx) {
                        Logger.e(TAG, "Failed to overwrite file with empty content", ioEx);
                    }
                }
            } catch (SecurityException secEx) {
                Logger.e(TAG, "Security exception when deleting external JSON file", secEx);
            }
        } else {
            Logger.w(TAG, "External JSON file no longer exists");
        }
    }

    /**
     * Copy file
     *
     * @param source Source file
     * @param dest Destination file
     * @throws IOException If an I/O error occurs
     */
    private void copyFile(File source, File dest) throws IOException {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[Constants.DEFAULT_BUFFER_SIZE];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
            fos.flush();
        }
    }

    /**
     * Encrypt and save configuration file
     *
     * @param mergedJsonObject Merged JSON object
     * @return Returns true if encryption and saving succeeded, otherwise returns false
     * @throws IOException If an I/O error occurs
     */
    private boolean encryptAndSaveConfig(JsonObject mergedJsonObject) throws IOException {
        // Convert merged JSON to string
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String mergedJsonConfig = gson.toJson(mergedJsonObject);
        Logger.d(TAG, "Merged JSON configuration length: " + mergedJsonConfig.length());

        // Create internal file directory (if it doesn't exist)
        File internalDir = getFilesDir();
        if (!internalDir.exists()) {
            internalDir.mkdirs();
        }

        // Create encrypted output file
        File encryptedConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
        Logger.d(TAG, "Encrypted output file path: " + encryptedConfigFile.getAbsolutePath());

        // Delete existing file if it exists
        if (encryptedConfigFile.exists()) {
            Logger.d(TAG, "Deleting existing encrypted configuration file");
            if (!encryptedConfigFile.delete()) {
                Logger.w(TAG, "Failed to delete existing encrypted configuration file");
            }
        }

        // Encrypt the configuration
        boolean encryptResult = encryptJsonToFile(mergedJsonConfig, encryptedConfigFile);
        if (!encryptResult) {
            return false;
        }

        return true;
    }

    /**
     * Encrypt JSON configuration to file
     *
     * @param jsonConfig JSON configuration string to encrypt
     * @param outputFile Output encrypted file
     * @return Returns true if encryption succeeded, otherwise returns false
     * @throws IOException If an I/O error occurs
     */
    private boolean encryptJsonToFile(String jsonConfig, File outputFile) throws IOException {
        // Create temporary file to store configuration data
        File tempInputFile = File.createTempFile("temp_config", ".json", getCacheDir());
        Logger.d(TAG, "Temporary input file path: " + tempInputFile.getAbsolutePath());

        try {
            // Write configuration data to temporary file
            if (!writeStringToFile(jsonConfig, tempInputFile)) {
                return false;
            }

            // Encrypt configuration file
            try (ParcelFileDescriptor inFd = ParcelFileDescriptor.open(tempInputFile,
                    ParcelFileDescriptor.MODE_READ_ONLY);
                    ParcelFileDescriptor outFd = ParcelFileDescriptor.open(outputFile,
                            ParcelFileDescriptor.MODE_CREATE | ParcelFileDescriptor.MODE_WRITE_ONLY)) {

                // Call encryption service interface to encrypt configuration file
                return executeFileEncryptService(FLAG_ENCRYPT, inFd, outFd);
            }
        } finally {
            // Delete temporary file
            if (tempInputFile.exists()) {
                tempInputFile.delete();
            }
        }
    }

    /**
     * Backup encrypted configuration file to external SD card
     *
     * @param encryptedConfigFile Encrypted configuration file
     * @return true if backup successful, false otherwise
     */
    private boolean backupEncryptedConfigToExternal(File encryptedConfigFile) {
        Logger.begin(TAG, "Backup encrypted config to external storage");

        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.w(TAG, "External SD card not available, cannot create backup");
            Logger.end(TAG, "Backup encrypted config to external storage", false);
            return false;
        }

        try {
            // Ensure external storage directory exists
            File externalDir = new File(externalConfigPath);
            if (!ensureDirectoryExists(externalDir)) {
                    Logger.e(TAG, "Failed to create external backup directory");
                    Logger.end(TAG, "Backup encrypted config to external storage", false);
                    return false;
            }

            // Create external backup file
            File externalBackupFile = new File(externalConfigPath, Constants.ENCRYPTED_CONFIG_FILENAME);
            Logger.d(TAG, "External backup file path: " + externalBackupFile.getAbsolutePath());

            // Copy file
            copyFile(encryptedConfigFile, externalBackupFile);
            Logger.i(TAG, "Encrypted config file backed up to external storage successfully");
            Logger.end(TAG, "Backup encrypted config to external storage", true);
            return true;

        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to backup encrypted config to external storage", ioEx);
            Logger.end(TAG, "Backup encrypted config to external storage", false);
            return false;
        }
    }

    /**
     * Schedule external backup with delay and retry mechanism.
     *
     * @param skipIfExists 如果外部存储中已存在 bin 文件，是否跳过备份
     */
    private void scheduleExternalBackupWithRetry(boolean skipIfExists) {
        Logger.i(TAG, "Scheduling external backup with 10s delay and retry, skipIfExists: " + skipIfExists);

        // Check if external bin file already exists and should skip
        if (skipIfExists && isExternalBinFileExists()) {
            Logger.i(TAG, "External bin file already exists, skipping backup as requested");
            return;
        }

        // Cancel previous task if exists
        if (mBackupRunnable != null) {
            mHandler.removeCallbacks(mBackupRunnable);
        }

        // Create backup task
        mBackupRunnable = () -> executeBackupWithRetry(1);

        // Execute after 10 seconds
        mHandler.postDelayed(mBackupRunnable, Constants.TEN_SECONDS_IN_MILLISECONDS);
    }

    /**
     * Check if external bin file already exists and is valid with unified retry mechanism.
     * This method handles SD card mount delays by using a unified retry approach.
     *
     * @return true if external bin file exists, is readable, and has valid size
     */
    private boolean isExternalBinFileExistsWithRetry() {
        Logger.d(TAG, "Request external bin file check with unified retry mechanism");

        // Try once immediately
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath != null) {
            Logger.d(TAG, "SD card available immediately for bin file, checking");
            return isExternalBinFileExists();
        }

        // SD card not available, request unified retry for bin file
        Logger.d(TAG, "SD card not available for bin file, requesting unified retry");
        requestUnifiedSdCardRetry(false, true);
        return false;
    }



    /**
     * Check if external bin file already exists and is valid.
     *
     * @return true if external bin file exists, is readable, and has valid size
     */
    private boolean isExternalBinFileExists() {
        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.d(TAG, "External SD card not available, external bin file does not exist");
            return false;
        }

        try {
            File externalBinFile = new File(externalConfigPath, Constants.ENCRYPTED_CONFIG_FILENAME);
            boolean exists = externalBinFile.exists() && externalBinFile.canRead() && externalBinFile.length() > 0;

            Logger.d(TAG, "External bin file exists check: " + exists
                    + " (path: " + externalBinFile.getAbsolutePath() + ")");

            if (exists) {
                Logger.d(TAG, "External bin file size: " + externalBinFile.length() + " bytes");
            }

            return exists;

        } catch (Exception es) {
            Logger.e(TAG, "Error checking external bin file existence", es);
            return false;
        }
    }

    /**
     * Execute backup with retry mechanism.
     *
     * @param attempt Current attempt number
     */
    private void executeBackupWithRetry(int attempt) {
        new Thread(() -> {
            try {
                Logger.d(TAG, "External backup attempt " + attempt + "/3");

                File encryptedConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);

                // Check if source file exists
                if (!encryptedConfigFile.exists()) {
                    Logger.e(TAG, "Source file not found, skipping backup");
                    return;
                }

                // Check external storage state
                String state = Environment.getExternalStorageState();
                if (!Environment.MEDIA_MOUNTED.equals(state)) {
                    throw new Exception("External storage not ready, state: " + state);
                }

                // Execute backup
                Logger.d(TAG, "Creating external backup of encrypted configuration");
                boolean backupSuccess = backupEncryptedConfigToExternal(encryptedConfigFile);
                if (backupSuccess) {
                    Logger.i(TAG, "External backup successful on attempt " + attempt);
                } else {
                    throw new Exception("Backup operation failed");
                }

            } catch (Exception es) {
                Logger.e(TAG, "Backup attempt " + attempt + " failed: " + es.getMessage());

                // Retry if failed and attempts remaining
                if (attempt < INT_3) {
                    mHandler.postDelayed(() -> executeBackupWithRetry(attempt + 1), INT_5000);
                } else {
                    Logger.e(TAG, "External backup failed after 3 attempts");
                }
            }
        }).start();
    }

    /**
     * Update cache and send notification broadcast.
     *
     * @param mergedJsonObject Merged JSON object
     * @param modifiedFields Modified fields
     * @param addedFields Added fields (now JsonObject instead of JsonArray)
     * @return Returns true if updated successfully, otherwise returns false
     */
    private boolean updateCacheAndNotify(
            JsonObject mergedJsonObject,
            JsonObject modifiedFields,
            JsonObject addedFields) {
        // Update configuration cache
        Logger.step(TAG, "Update configuration cache");
        try {
            // Clear existing cache
            mConfigCache.clear();

            // Add all items from merged JSON to cache
            for (Map.Entry<String, JsonElement> entry : mergedJsonObject.entrySet()) {
                mConfigCache.put(entry.getKey(), entry.getValue());
                Logger.d(TAG, "Cache configuration item: " + entry.getKey());
            }

            // Send broadcast to notify configuration changes
            Logger.step(TAG, "Send broadcast to notify configuration changes");
            sendConfigChangedBroadcast(modifiedFields, addedFields);

            Logger.d(TAG, "Update configuration cache completed, " + mConfigCache.size() + " items");
            return true;

        } catch (JsonSyntaxException ex) {
            Logger.e(TAG, "Cache configuration parsing JSON failed", ex);
            return false;
        }
    }

    /**
     * Send broadcast to notify configuration changes.
     *
     * @param modifiedFields Modified configuration fields
     * @param addedFields Added configuration fields
     */
    private void sendConfigChangedBroadcast(JsonObject modifiedFields, JsonObject addedFields) {
        // Check if there are any modified or added items, if not, don't send broadcast
        if (modifiedFields.size() == 0 && addedFields.size() == 0) {
            Logger.d(TAG, "No modified or added fields, skip sending broadcast");
            return;
        }

        Logger.begin(TAG, "Send configuration changed broadcast");

        try {
            // Create intent with action
            Intent intent = new Intent(Constants.CONFIG_CHANGED_ACTION);

            // Convert modified fields to JSON string
            if (modifiedFields.size() > 0) {
                Gson gson = new GsonBuilder().create();
                String modifiedJson = gson.toJson(modifiedFields);
                intent.putExtra("modified", modifiedJson);
                Logger.d(TAG, "Modified fields JSON: " + modifiedJson);
            }

            // Convert added fields to JSON string
            if (addedFields.size() > 0) {
                Gson gson = new GsonBuilder().create();
                String addedJson = gson.toJson(addedFields);
                intent.putExtra("added", addedJson);
                Logger.d(TAG, "Added fields JSON: " + addedJson);
            }

            // Send broadcast
            sendBroadcast(intent);
            Logger.i(TAG, "Configuration changed broadcast sent successfully");
        } catch (Exception ex) {
            Logger.e(TAG, "Failed to send configuration changed broadcast", ex);
        }

        Logger.end(TAG, "Send configuration changed broadcast", true);
    }

    /**
     * Get configuration value by key.
     */
    private String getConfigValue(String key) {
        // Check if config cache is empty or not initialized
        if (mConfigCache == null || mConfigCache.isEmpty()) {
            Logger.w(TAG, "Configuration cache is empty or not initialized, attempting to reload");

            // Try to reload configuration
            synchronized (this) {
                if (mConfigCache == null || mConfigCache.isEmpty()) {
                    try {
                        checkAndInitializeConfig();
                        if (mConfigCache == null || mConfigCache.isEmpty()) {
                            Logger.e(TAG, "Failed to reload configuration, cache still empty");
                            return "{\"error\":\"NO_CONFIG\",\"errorCode\":" + Constants.ERROR_NO_CONFIG 
                                + ",\"message\":\"No configuration available\"}";
                        }
                        Logger.i(TAG, "Successfully reloaded configuration cache");
                    } catch (Exception es) {
                        Logger.e(TAG, "Exception during configuration reload", es);
                        return "{\"error\":\"NO_CONFIG\",\"errorCode\":" + Constants.ERROR_NO_CONFIG
                            + ",\"message\":\"Configuration reload failed: " + es.getMessage() + "\"}";
                    }
                }
            }
        }

        // Handle special access patterns
        if (key.contains("[")
            && key.contains("]")
            && key.contains(".")) {
            String result = getComplexPathValue(key);
            if (result == null) {
                return "{\"error\":\"KEY_NOT_FOUND\",\"errorCode\":" + Constants.ERROR_KEY_NOT_FOUND
                    + ",\"message\":\"Key not found: " + key + "\"}";
            }
            return result;
        }

        if (key.contains("[") 
            && key.endsWith("]")) {
            String result = getArrayElementValue(key);
            if (result == null) {
                return "{\"error\":\"KEY_NOT_FOUND\",\"errorCode\":" + Constants.ERROR_KEY_NOT_FOUND 
                    + ",\"message\":\"Key not found: " + key + "\"}";
            }
            return result;
        }

        if (key.contains(".")) {
            String result = getNestedPropertyValue(key);
            if (result == null) {
                return "{\"error\":\"KEY_NOT_FOUND\",\"errorCode\":" + Constants.ERROR_KEY_NOT_FOUND 
                    + ",\"message\":\"Key not found: " + key + "\"}";
            }
            return result;
        }

        // Try to get from cache
        String result = getValueFromCache(key);
        if (result != null) {
            return result;
        }

        // Log cache status for debugging
        Logger.w(TAG, "Configuration key does not exist: " + key);
        Logger.d(TAG, "Current cache size: " + (mConfigCache != null ? mConfigCache.size() : 0));
        if (mConfigCache != null && !mConfigCache.isEmpty()) {
            Logger.d(TAG, "Available keys in cache: " + mConfigCache.keySet().toString());
        }

        return "{\"error\":\"KEY_NOT_FOUND\",\"errorCode\":" + Constants.ERROR_KEY_NOT_FOUND 
            + ",\"message\":\"Key not found: " + key + "\"}";
    }

    /**
     * Get value from cache directly.
     */
    private String getValueFromCache(String key) {
        if (mConfigCache.containsKey(key)) {
            JsonElement value = mConfigCache.get(key);
            if (value != null) {
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
                return gson.toJson(value);
        }
        }
        return null;
    }

    /**
     * Handle complex path access, such as "arrayName[index].property"
     */
    private String getComplexPathValue(String key) {
        try {
            // Find the position of the first bracket
            int firstBracketIndex = key.indexOf('[');
            if (firstBracketIndex <= 0) {
                Logger.w(TAG, "Invalid complex path format: " + key);
                return null;
            }

            // Get array name and element
            String arrayName = key.substring(0, firstBracketIndex);
            JsonElement arrayElement = getArrayElement(arrayName);
            if (arrayElement == null) {
                        return null;
            }

            // Parse index
            int closeBracketIndex = key.indexOf(']', firstBracketIndex);
            if (closeBracketIndex <= firstBracketIndex) {
                Logger.w(TAG, "Invalid array index format: " + key);
                return null;
            }

            String indexStr = key.substring(firstBracketIndex + 1, closeBracketIndex);
            int index;
            try {
                index = Integer.parseInt(indexStr);
            } catch (NumberFormatException numEx) {
                Logger.w(TAG, "Invalid array index: " + indexStr);
                return null;
            }

            // Get array element
            JsonArray jsonArray = arrayElement.getAsJsonArray();
            if (index < 0 || index >= jsonArray.size()) {
                Logger.w(TAG, "Array index out of bounds: " + index + ", array size: " + jsonArray.size());
                return null;
            }

            JsonElement arrayItem = jsonArray.get(index);
            if (!arrayItem.isJsonObject()) {
                Logger.w(TAG, "Array element is not object, cannot access property: " + key);
                return null;
            }

            // Get property path
            if (closeBracketIndex + 1 >= key.length() || key.charAt(closeBracketIndex + 1) != '.') {
                Logger.w(TAG, "Invalid property path format: " + key);
                return null;
            }

            String propertyPath = key.substring(closeBracketIndex + 2); // Skip '].'

            // Navigate through nested properties
            JsonObject itemObject = arrayItem.getAsJsonObject();
            JsonElement result = getNestedProperty(itemObject, propertyPath);

            if (result != null) {
                return new GsonBuilder().setPrettyPrinting().create().toJson(result);
            }
            return null;

        } catch (Exception ex) {
            Logger.e(TAG, "Get complex path value exception", ex);
                    return null;
        }
    }

    /**
     * Get array element from cache.
     */
    private JsonElement getArrayElement(String arrayName) {
        JsonElement arrayElement = mConfigCache.get(arrayName);
        if (arrayElement != null && arrayElement.isJsonArray()) {
            return arrayElement;
        }

        Logger.w(TAG, "Array not found or element is not array type: " + arrayName);
        return null;
    }

    /**
     * Get array element value by key.
     */
    private String getArrayElementValue(String key) {
        try {
            // Find the position of the first bracket
            int firstBracketIndex = key.indexOf('[');
            if (firstBracketIndex <= 0) {
                Logger.w(TAG, "Invalid array access format: " + key);
                return null;
            }

            // Get array name
            String arrayName = key.substring(0, firstBracketIndex);
            JsonElement arrayElement = getArrayElement(arrayName);
            if (arrayElement == null) {
                return null;
            }

            // Parse index
            int closeBracketIndex = key.indexOf(']', firstBracketIndex);
            if (closeBracketIndex <= firstBracketIndex) {
                Logger.w(TAG, "Invalid array index format: " + key);
                        return null;
                    }

            String indexStr = key.substring(firstBracketIndex + 1, closeBracketIndex);
            int index;
            try {
                index = Integer.parseInt(indexStr);
            } catch (NumberFormatException numEx) {
                Logger.w(TAG, "Invalid array index: " + indexStr);
                    return null;
            }

            // Get array element
            JsonArray jsonArray = arrayElement.getAsJsonArray();
            if (index < 0 || index >= jsonArray.size()) {
                Logger.w(TAG, "Array index out of bounds: " + index + ", array size: " + jsonArray.size());
                return null;
            }

            JsonElement arrayItem = jsonArray.get(index);
            return new GsonBuilder().setPrettyPrinting().create().toJson(arrayItem);
        } catch (Exception ex) {
            Logger.e(TAG, "Get array element value exception", ex);
            return null;
        }
    }

    /**
     * Get nested property value
     * Support format: "parent.child" or "parent.child.grandchild"
     *
     * @param key Point-separated property path
     * @return Property value, returns null if not found
     */
    private String getNestedPropertyValue(String key) {
        Logger.begin(TAG, "Get nested property value");
        Logger.param(TAG, "key", key);

        try {
            String[] parts = key.split("\\.", 2);
            String parentKey = parts[0];
            String childPath = parts[1];

            Logger.d(TAG, "Parse nested property: parent key=" + parentKey + ", child path=" + childPath);

            // Get parent object
            JsonElement parentElement = mConfigCache.get(parentKey);
            if (parentElement == null) {
                Logger.w(TAG, "Parent key not found in cache: " + parentKey);
                Logger.end(TAG, "Get nested property value", false);
                return null;
            }

            if (!parentElement.isJsonObject()) {
                Logger.w(TAG, "Parent key is not object type: " + parentKey);
                Logger.end(TAG, "Get nested property value", false);
                return null;
            }

            JsonObject parentObject = parentElement.getAsJsonObject();
            JsonElement propertyValue = getNestedProperty(parentObject, childPath);

            if (propertyValue != null) {
                Gson gson = new GsonBuilder().setPrettyPrinting().create();
                String result = gson.toJson(propertyValue);
                Logger.d(TAG, "Get nested property value, length: " + result.length());
                Logger.end(TAG, "Get nested property value", true);
                return result;
            } else {
                Logger.w(TAG, "Not find nested property: " + childPath);
                Logger.end(TAG, "Get nested property value", false);
                return null;
            }

        } catch (Exception ex) {
            Logger.e(TAG, "Get nested property value exception", ex);
            Logger.end(TAG, "Get nested property value", false);
            return null;
        }
    }

    /**
     * Recursively get nested property
     *
     * @param jsonObject JSON object
     * @param propertyPath Property path
     * @return Property value, returns null if not found
     */
    private JsonElement getNestedProperty(JsonObject jsonObject, String propertyPath) {
        Logger.methodIn(TAG, "getNestedProperty");
        Logger.param(TAG, "propertyPath", propertyPath);

        if (propertyPath.contains(".")) {
            String[] parts = propertyPath.split("\\.", 2);
            String currentKey = parts[0];
            String remainingPath = parts[1];

            Logger.d(TAG, "Continue parsing nested property: current key=" + currentKey
                    + ", remaining path=" + remainingPath);

            if (jsonObject.has(currentKey)) {
                JsonElement element = jsonObject.get(currentKey);
                if (element.isJsonObject()) {
                    JsonElement result = getNestedProperty(element.getAsJsonObject(), remainingPath);
                    Logger.methodOut(TAG, "getNestedProperty");
                    return result;
                } else {
                    Logger.w(TAG, "Property is not object type, cannot continue accessing nested property: " 
                        + currentKey);
                    Logger.methodOut(TAG, "getNestedProperty");
                    return null;
                }
            } else {
                Logger.w(TAG, "Object does not have property: " + currentKey);
                Logger.methodOut(TAG, "getNestedProperty");
                return null;
            }
        } else {
            // Final property
            if (jsonObject.has(propertyPath)) {
                JsonElement result = jsonObject.get(propertyPath);
                Logger.d(TAG, "Find final property: " + propertyPath);
                Logger.methodOut(TAG, "getNestedProperty");
                return result;
            } else {
                Logger.w(TAG, "Object does not have final property: " + propertyPath);
                Logger.methodOut(TAG, "getNestedProperty");
                return null;
            }
        }
    }

    /**
     * Load configuration to cache.
     */
    private boolean loadConfigToCache() {
        // If configuration has been successfully loaded before, do not reload
        if (mConfigCache != null && !mConfigCache.isEmpty()) {
            Logger.d(TAG, "Configuration has been loaded, using cache directly");
            return true;
        }

        // First try to load from internal encrypted configuration file
        if (loadFromInternalEncrypted()) {
            return true;
        }

        // Then try to load from external encrypted configuration file
        if (loadFromExternalEncrypted()) {
            return true;
        }

        // Try to load from external JSON file
        if (loadFromExternalJson()) {
            return true;
        }

        // Last attempt, use the application's external storage directory
        if (loadFromAppExternalStorage()) {
            return true;
        }

        // Final fallback: load from assets and regenerate encrypted files
        Logger.w(TAG, "All configuration sources failed, attempting to regenerate from assets");
        if (regenerateConfigFromAssets()) {
            return true;
        }

        Logger.e(TAG, "No configuration file found in any location, initialization failed");
        return false;
    }

    /**
     * Load configuration from internal encrypted file.
     */
    private boolean loadFromInternalEncrypted() {
        File encryptedConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
        if (!encryptedConfigFile.exists()) {
            Logger.d(TAG, "Encrypted configuration file does not exist in internal storage");
            return false;
        }

        Logger.d(TAG, "Encrypted configuration file size: " + encryptedConfigFile.length() + " bytes");

        // Skip decryption if previous attempts failed
        if (mServiceRetryFailed) {
            Logger.w(TAG, "Skipping internal encrypted config decryption due to previous service failure");
            return false;
        }

        try {
            // Decrypt configuration file
            String decryptedConfig = decryptConfigFile(encryptedConfigFile);
            if (decryptedConfig != null && !decryptedConfig.isEmpty()) {
                // Validate configuration integrity first
                if (!validateConfigurationIntegrity(decryptedConfig)) {
                    Logger.w(TAG, "Internal encrypted config validation failed: missing or invalid configVersion");
                    // Remove corrupted internal file
                    if (encryptedConfigFile.delete()) {
                        Logger.i(TAG, "Deleted corrupted internal encrypted config file");
                    }
                    return false;
                }

                // Parse JSON to configuration cache
                boolean parseResult = parseJsonToCache(decryptedConfig);
                if (parseResult) {
                    Logger.i(TAG, "Successfully loaded and validated configuration from internal encrypted file");
                    return true;
                } else {
                    Logger.e(TAG, "Failed to parse decrypted configuration");
                    // Set failure flag on parsing error to avoid repeated attempts
                    mServiceRetryFailed = true;
                }
            } else {
                Logger.e(TAG, "Failed to decrypt configuration file");
                // Set failure flag on decryption error to avoid repeated attempts
                mServiceRetryFailed = true;
            }
        } catch (IOException ioEx) {
            Logger.e(TAG, "Error reading or decrypting configuration file", ioEx);
            // Set failure flag on exception to avoid repeated attempts
            mServiceRetryFailed = true;
        } catch (Exception ex) {
            Logger.e(TAG, "Unexpected exception in loadFromInternalEncrypted", ex);
            // Set failure flag on exception to avoid repeated attempts
            mServiceRetryFailed = true;
            return false;
        }
        return false;
    }

    /**
     * Load configuration from external encrypted file.
     */
    private boolean loadFromExternalEncrypted() {
        // Skip decryption if previous attempts failed
        if (mServiceRetryFailed) {
            return false;
        }

        // Check if external SD card is available
        String externalConfigPath = Constants.getExternalConfigPath();
        if (externalConfigPath == null) {
            Logger.d(TAG, "External SD card not available, cannot load from external encrypted file");
            return false;
        }

        File externalEncryptedConfigFile = new File(externalConfigPath,
                Constants.ENCRYPTED_CONFIG_FILENAME);
        Logger.d(TAG, "External encrypted config file path: " + externalEncryptedConfigFile.getAbsolutePath());

        if (!externalEncryptedConfigFile.exists() 
            || !externalEncryptedConfigFile.canRead()) {
            Logger.d(TAG, "No external encrypted config file or cannot read");
            return false;
        }

        Logger.d(TAG, "External encrypted configuration file size: " + externalEncryptedConfigFile.length() + " bytes");

        try {
            // Decrypt configuration file
            String decryptedConfig = decryptConfigFile(externalEncryptedConfigFile);
            if (decryptedConfig != null && !decryptedConfig.isEmpty()) {
                // Validate configuration integrity first
                if (!validateConfigurationIntegrity(decryptedConfig)) {
                    Logger.w(TAG, "External encrypted config validation failed: missing or invalid configVersion");
                    // Remove corrupted external file
                    if (externalEncryptedConfigFile.delete()) {
                        Logger.i(TAG, "Deleted corrupted external encrypted config file");
                    }
                    return false;
                }

                // Parse JSON to configuration cache
                boolean parseResult = parseJsonToCache(decryptedConfig);
                if (parseResult) {
                    Logger.i(TAG, "Successfully loaded and validated configuration from external encrypted file");

                    // Backup to internal storage
                    File encryptedConfigFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
                    if (!encryptedConfigFile.exists() || encryptedConfigFile.length() == 0) {
                        copyFile(externalEncryptedConfigFile, encryptedConfigFile);
                        Logger.i(TAG, "Backed up external encrypted configuration to internal storage");
                    }

                    return true;
                } else {
                    Logger.e(TAG, "Failed to parse decrypted external configuration");
                }
            } else {
                Logger.e(TAG, "Failed to decrypt external configuration file");
                // Set failure flag to avoid repeated attempts
                mServiceRetryFailed = true;
            }
        } catch (IOException ioEx) {
            Logger.e(TAG, "Error reading or decrypting external configuration file", ioEx);
            // Set failure flag to avoid repeated attempts
            mServiceRetryFailed = true;
        }

        return false;
    }

    /**
     * Load configuration from app's external storage.
     */
    private boolean loadFromAppExternalStorage() {
        try {
            File appExternalDir = getExternalFilesDir(null);
            if (appExternalDir == null) {
                return false;
            }

            File appExternalConfigFile = new File(appExternalDir, Constants.EXTERNAL_CONFIG_FILENAME);
            Logger.d(TAG, "Trying to use app's external storage directory: "
                    + appExternalConfigFile.getAbsolutePath());

            if (!appExternalConfigFile.exists()
                || !appExternalConfigFile.canRead()) {
                return false;
            }

            String appExternalConfig = readFileToString(appExternalConfigFile);
            if (appExternalConfig == null
                || appExternalConfig.isEmpty()) {
                return false;
            }

            // Check version compatibility before processing
            if (!isExternalConfigVersionCompatible(appExternalConfig)) {
                Logger.w(TAG, "App external config version is not higher than current version, skipping");
                return false;
            }

            boolean parseResult = parseJsonToCache(appExternalConfig);
            if (!parseResult) {
                return false;
            }

            Logger.i(TAG, "Successfully loaded configuration from app's external storage");

            // After successfully loading configuration from app's external storage, reset failure flag
            // to try encryption
            mServiceRetryFailed = false;
            Logger.d(TAG, "Reset service retry flag to allow encryption attempt for app external config");

            try {
                Logger.d(TAG, "Attempting to encrypt app external storage configuration");
                boolean saveResult = saveConfig(appExternalConfig, true); // Enable retry for internal loading
                if (saveResult) {
                    Logger.i(TAG, "Successfully encrypted and saved configuration from app external storage");
                }
            } catch (Exception ex) {
                Logger.w(TAG, "Exception while saving app external config to internal encrypted file", ex);
            }

            appExternalConfigFile.delete();
            return true;
        } catch (Exception ex) {
            Logger.e(TAG, "Error accessing app's external storage", ex);
        }

        return false;
    }

    /**
     * Decrypt configuration file.
     */
    private String decryptConfigFile(File encryptedConfigFile) throws IOException {
        if (!encryptedConfigFile.exists() 
            || !encryptedConfigFile.canRead()) {
            Logger.e(TAG, "Encrypted configuration file does not exist or cannot be read");
            return null;
        }

        // If it has previously attempted and failed, return null directly
        if (mServiceRetryFailed) {
            Logger.w(TAG, "Previous decryption attempt failed, skipping this operation");
            return null;
        }

        // Create temporary file for decryption output
        File tempOutputFile = File.createTempFile("temp_config_decrypted", ".json", getCacheDir());

        try {
            // Decrypt configuration file
            try (ParcelFileDescriptor inFd = ParcelFileDescriptor.open(encryptedConfigFile,
                    ParcelFileDescriptor.MODE_READ_ONLY);
                 ParcelFileDescriptor outFd = ParcelFileDescriptor.open(tempOutputFile,
                    ParcelFileDescriptor.MODE_WRITE_ONLY | ParcelFileDescriptor.MODE_CREATE)) {

                // Call the encryption service
                boolean decryptResult = executeFileEncryptService(FLAG_DECRYPT, inFd, outFd);

                if (!decryptResult) {
                    Logger.e(TAG, "Failed to decrypt configuration file");
                    mServiceRetryFailed = true;
                    return null;
                }
            }

            // Read decrypted content from temporary file
            return readFileToString(tempOutputFile);
        } catch (Exception ex) {
            Logger.e(TAG, "Exception during decryption process", ex);
            mServiceRetryFailed = true;
            return null;
        } finally {
            // Ensure temporary file is deleted
            if (tempOutputFile.exists()) {
                tempOutputFile.delete();
            }
        }
    }

    /**
     * Read file to string.
     *
     * @param file The file to read
     * @return The file content as string
     * @throws IOException If an I/O error occurs
     */
    private String readFileToString(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[Constants.DEFAULT_BUFFER_SIZE];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                baos.write(buffer, 0, len);
            }
            return new String(baos.toByteArray(), StandardCharsets.UTF_8);
        }
    }

    // Add a retry control flag
    private boolean mServiceRetryFailed = false;

    /**
     * If a save operation is already in progress, avoid duplicate attempts.
     * If encryption service is not bound, try to bind it.
     */
    private void tryBindEncryptService() {
        // If a save operation is already in progress, avoid duplicate attempts
        if (mIsSaving) {
            Logger.d(TAG, "Already in saving process, skip binding");
            return;
        }

        // Avoid binding service multiple times
        if (mEncryptServiceBound) {
            Logger.d(TAG, "Encryption service already bound");
            return;
        }

        if (mFileEncryptManager != null) {
            Logger.d(TAG, "Trying to bind FileEncrypt service");
            try {
                // bindService method returns void, not boolean
                mFileEncryptManager.bindService(this);
                // Service connection status will be notified via callback
                Logger.d(TAG, "FileEncrypt service bind request sent");
            } catch (Exception ex) {
                Logger.e(TAG, "Exception while binding FileEncrypt service", ex);
                mServiceRetryFailed = true;  // Set failure flag on exception
            }
        } else {
            Logger.w(TAG, "FileEncrypt manager is null, cannot bind");
        }
    }

    /**
     * Parse JSON string to cache.
     *
     * @param jsonConfig JSON configuration string
     * @return Returns true if parsing was successful
     */
    private boolean parseJsonToCache(String jsonConfig) {
        try {
            JsonObject jsonObject = JsonParser.parseString(jsonConfig).getAsJsonObject();

            // Clear existing cache
            mConfigCache.clear();

            // Add all top-level elements to cache
            for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
                mConfigCache.put(entry.getKey(), entry.getValue());
                Logger.d(TAG, "Cache configuration item: " + entry.getKey());
            }

            Logger.i(TAG, "Parse JSON to cache completed, " + mConfigCache.size() + " items");
            return true;

        } catch (JsonSyntaxException ex) {
            Logger.e(TAG, "Parse JSON to cache failed", ex);
            return false;
        }
    }

    /**
     * Check if JSON string is valid.
     *
     * @param json JSON string to validate
     * @return Returns true if JSON is valid
     */
    private boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }

        try {
            JsonParser.parseString(json);
            return true;
        } catch (JsonSyntaxException ex) {
            return false;
        }
    }

    /**
     * Regenerate configuration from assets file and create encrypted backups.
     *
     * @return Returns true if regeneration was successful
     */
    private boolean regenerateConfigFromAssets() {
        Logger.begin(TAG, "Regenerate configuration from assets");

        try {
            // Read configuration from assets
            InputStream assetsInputStream = getAssets().open("config.json");
            StringBuilder stringBuilder = new StringBuilder();
            BufferedReader reader = new BufferedReader(new InputStreamReader(assetsInputStream,
                    StandardCharsets.UTF_8));

            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line).append('\n');
            }
            reader.close();
            assetsInputStream.close();

            String assetsConfigJson = stringBuilder.toString();
            Logger.d(TAG, "Assets config JSON length: " + assetsConfigJson.length());

            // Validate assets configuration
            if (!validateConfigurationIntegrity(assetsConfigJson)) {
                Logger.e(TAG, "Assets configuration validation failed: missing or invalid configVersion");
                Logger.end(TAG, "Regenerate configuration from assets", false);
                return false;
            }

            // Parse to cache first
            boolean parseResult = parseJsonToCache(assetsConfigJson);
            if (!parseResult) {
                Logger.e(TAG, "Failed to parse assets configuration");
                Logger.end(TAG, "Regenerate configuration from assets", false);
                return false;
            }

            // Try to encrypt and save with retry mechanism for internal loading
            try {
                if (mEncryptServiceBound && checkEncryptionServiceAvailable()) {
                    Logger.d(TAG, "Attempting to encrypt assets configuration during regeneration");
                    boolean saveResult = saveConfig(assetsConfigJson, true); // Enable retry for internal loading
                    if (saveResult) {
                        Logger.i(TAG, "Successfully regenerated and encrypted configuration from assets");

                        // Also create external backup
                        scheduleExternalBackupWithRetry(false);
                    }
                } else {
                    Logger.w(TAG, "Encryption service not available during assets regeneration");
                }
            } catch (Exception ex) {
                Logger.e(TAG, "Exception during assets regeneration encryption", ex);
            }

            Logger.i(TAG, "Successfully regenerated configuration from assets");
            Logger.end(TAG, "Regenerate configuration from assets", true);
            return true;

        } catch (IOException ioEx) {
            Logger.e(TAG, "Failed to read assets configuration", ioEx);
            Logger.end(TAG, "Regenerate configuration from assets", false);
            return false;
        } catch (Exception ex) {
            Logger.e(TAG, "Unexpected error during assets regeneration", ex);
            Logger.end(TAG, "Regenerate configuration from assets", false);
            return false;
        }
    }

    /**
     * Validate encrypted configuration file by attempting to decrypt and check configVersion.
     *
     * @return Returns true if encrypted file can be successfully decrypted and contains configVersion
     */
    private boolean validateEncryptedConfigFile() {
        try {
            File encryptedFile = new File(getFilesDir(), Constants.ENCRYPTED_CONFIG_FILENAME);
            if (!encryptedFile.exists()) {
                Logger.w(TAG, "Encrypted config file does not exist for validation");
                return false;
            }

            // Try to decrypt the file
            String decryptedConfig = decryptConfigFile(encryptedFile);
            if (decryptedConfig == null) {
                Logger.w(TAG, "Failed to decrypt config file for validation");
                return false;
            }

            // Validate the decrypted configuration
            boolean isValid = validateConfigurationIntegrity(decryptedConfig);
            if (isValid) {
                Logger.i(TAG, "Encrypted configuration file validation successful");
            } else {
                Logger.w(TAG, "Encrypted configuration file validation failed: missing or invalid configVersion");
            }
            return isValid;

        } catch (IOException ioEx) {
            Logger.e(TAG, "IOException during encrypted config file validation", ioEx);
            return false;
        } catch (Exception ex) {
            Logger.e(TAG, "Exception during encrypted config file validation", ex);
            return false;
        }
    }

    /**
     * Validate if configuration contains required configVersion field.
     *
     * @param jsonConfig JSON configuration string to validate
     * @return Returns true if configuration is valid (contains configVersion)
     */
    private boolean validateConfigurationIntegrity(String jsonConfig) {
        if (jsonConfig == null || jsonConfig.trim().isEmpty()) {
            Logger.w(TAG, "Configuration validation failed: empty or null configuration");
            return false;
        }

        try {
            JsonObject jsonObject = JsonParser.parseString(jsonConfig).getAsJsonObject();

            if (!jsonObject.has("configVersion")) {
                Logger.w(TAG, "Configuration validation failed: missing 'configVersion' field");
                return false;
            }

            JsonElement versionElement = jsonObject.get("configVersion");
            if (versionElement.isJsonNull() || versionElement.getAsString().trim().isEmpty()) {
                Logger.w(TAG, "Configuration validation failed: 'configVersion' field is null or empty");
                return false;
            }

            String version = versionElement.getAsString();
            Logger.d(TAG, "Configuration validation successful: configVersion = " + version);
            return true;

        } catch (JsonSyntaxException ex) {
            Logger.e(TAG, "Configuration validation failed: invalid JSON format", ex);
            return false;
        }
    }

    /**
     * Check if external configuration version is compatible (higher than current version).
     *
     * @param externalConfigJson External configuration JSON string
     * @return Returns true if external version is higher than current version
     */
    private boolean isExternalConfigVersionCompatible(String externalConfigJson) {
        Logger.begin(TAG, "Check external config version compatibility");

        try {
            // Get external config version
            String externalVersion = getVersionFromConfig(externalConfigJson);
            if (externalVersion == null) {
                Logger.w(TAG, "External config does not contain configVersion field");
                Logger.end(TAG, "Check external config version compatibility", false);
                return false;
            }

            // Get current config version
            String currentVersion = getCurrentConfigVersion();
            if (currentVersion == null) {
                Logger.i(TAG, "No current config version found, accepting external config");
                Logger.end(TAG, "Check external config version compatibility", true);
                return true;
            }

            // Compare versions
            boolean isHigher = isVersionHigher(externalVersion, currentVersion);
            Logger.i(TAG, "Version comparison: external=" + externalVersion
                    + ", current=" + currentVersion + ", isHigher=" + isHigher);

            Logger.end(TAG, "Check external config version compatibility", isHigher);
            return isHigher;

        } catch (Exception ex) {
            Logger.e(TAG, "Error during version compatibility check", ex);
            Logger.end(TAG, "Check external config version compatibility", false);
            return false;
        }
    }

    /**
     * Get version string from configuration JSON.
     *
     * @param configJson Configuration JSON string
     * @return Version string or null if not found
     */
    private String getVersionFromConfig(String configJson) {
        try {
            JsonObject jsonObject = JsonParser.parseString(configJson).getAsJsonObject();
            if (jsonObject.has("configVersion")) {
                JsonElement versionElement = jsonObject.get("configVersion");
                if (!versionElement.isJsonNull()) {
                    return versionElement.getAsString().trim();
                }
            }
        } catch (JsonSyntaxException ex) {
            Logger.e(TAG, "Failed to parse config JSON for version", ex);
        }
        return null;
    }

    /**
     * Get current configuration version from cache.
     *
     * @return Current version string or null if not found
     */
    private String getCurrentConfigVersion() {
        if (mConfigCache != null && mConfigCache.containsKey("configVersion")) {
            JsonElement versionElement = mConfigCache.get("configVersion");
            if (versionElement != null && !versionElement.isJsonNull()) {
                return versionElement.getAsString().trim();
            }
        }
        return null;
    }

    /**
     * Compare two version strings to determine if the first is higher than the second.
     * Supports version formats like "V001", "V002", etc.
     *
     * @param version1 First version string
     * @param version2 Second version string
     * @return Returns true if version1 is higher than version2
     */
    private boolean isVersionHigher(String version1, String version2) {
        if (version1 == null || version2 == null) {
            return false;
        }

        try {
            // Extract numeric part from version strings (e.g., "V001" -> "001")
            String num1 = extractVersionNumber(version1);
            String num2 = extractVersionNumber(version2);

            if (num1 == null || num2 == null) {
                // Fallback to string comparison if numeric extraction fails
                return version1.compareTo(version2) > 0;
            }

            // Convert to integers for comparison
            int intVersion1 = Integer.parseInt(num1);
            int intVersion2 = Integer.parseInt(num2);

            Logger.d(TAG, "Version comparison: " + version1 + "(" + intVersion1 + ") vs "
                    + version2 + "(" + intVersion2 + ")");

            return intVersion1 > intVersion2;

        } catch (NumberFormatException ex) {
            Logger.w(TAG, "Failed to parse version numbers, using string comparison", ex);
            // Fallback to string comparison
            return version1.compareTo(version2) > 0;
        }
    }

    /**
     * Extract numeric part from version string.
     * Supports formats like "V001", "v1.2.3", "1.0", etc.
     *
     * @param version Version string
     * @return Numeric part as string or null if not found
     */
    private String extractVersionNumber(String version) {
        if (version == null || version.isEmpty()) {
            return null;
        }

        // Remove common prefixes and extract numbers
        String cleaned = version.toUpperCase().trim();

        // Handle "V001" format
        if (cleaned.startsWith("V") && cleaned.length() > 1) {
            String numPart = cleaned.substring(1);
            if (numPart.matches("\\d+")) {
                return numPart;
            }
        }

        // Handle pure numeric format "001", "123", etc.
        if (cleaned.matches("\\d+")) {
            return cleaned;
        }

        // For more complex formats, extract first sequence of digits
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(cleaned);
        if (matcher.find()) {
            return matcher.group();
        }

        return null;
    }



    /**
     * Check if encryption service is available.
     *
     * @return Returns true if encryption service is available, otherwise returns false
     */
    private boolean checkEncryptionServiceAvailable() {
        if (!mEncryptServiceBound || mFileEncryptManager == null) {
            Logger.e(TAG, "File encryption service not bound or service interface is null, " 
                + "cannot save encrypted configuration");
            return false;
        }

        Logger.d(TAG, "Encryption service is available");
        return true;
    }

    /**
     * Execute file encryption/decryption service.
     *
     * @param flag Encryption/Decryption flag (0: Encrypt, 1: Decrypt)
     * @param inFd Input file descriptor
     * @param outFd Output file descriptor
     * @return Returns true if the operation succeeded, otherwise returns false
     */
    private boolean executeFileEncryptService(int flag, ParcelFileDescriptor inFd, ParcelFileDescriptor outFd) {
        // If encryption service is not bound, return failure directly
        if (!mEncryptServiceBound || mFileEncryptManager == null) {
            Logger.w(TAG, "Encryption service not bound, cannot execute operation");
            return false;
        }

        // Check parameters
        if (inFd == null || outFd == null) {
            Logger.e(TAG, "Invalid file descriptors");
            return false;
        }

        // Log file descriptors for debugging
        logFileDescriptorInfo(inFd, outFd);

        // Call encryption service
        return callEncryptionService(flag, inFd, outFd);
    }

    /**
     * Log file descriptor information for debugging.
     *
     * @param inFd Input file descriptor
     * @param outFd Output file descriptor
     */
    private void logFileDescriptorInfo(ParcelFileDescriptor inFd, ParcelFileDescriptor outFd) {
        try {
            String inFdInfo = inFd.toString();
            String outFdInfo = outFd.toString();
            Logger.d(TAG, "Input file descriptor: " + inFdInfo);
            Logger.d(TAG, "Output file descriptor: " + outFdInfo);

            // Check file descriptor status
            try {
                Logger.d(TAG, "Input FD status check: " + (inFd.getFd() > 0 ? "valid" : "invalid"));
                Logger.d(TAG, "Output FD status check: " + (outFd.getFd() > 0 ? "valid" : "invalid"));
            } catch (Exception ex) {
                Logger.w(TAG, "Check file descriptor status failed", ex);
            }
        } catch (Exception ex) {
            Logger.w(TAG, "Get file descriptor information failed", ex);
        }
    }

    /**
     * Call encryption service to perform encryption/decryption.
     */
    private boolean callEncryptionService(int flag, ParcelFileDescriptor inFd, ParcelFileDescriptor outFd) {
        try {
            // Convert internal flags to boolean values required by FileEncryptServiceManager API
            // FLAG_ENCRYPT(0) corresponds to true, FLAG_DECRYPT(1) corresponds to false
            boolean isEncryption = (flag == FLAG_ENCRYPT);
            Logger.d(TAG, "Operation: " + (isEncryption ? "encryption" : "decryption"));

            // Try service call
            Logger.step(TAG, "Call remote service method (isEncryption=" + isEncryption + ")");
            boolean result = mFileEncryptManager.processStream(isEncryption, inFd, outFd);
            Logger.d(TAG, "Remote service call result: " + result);
            return result;
        } catch (SecurityException se) {
            Logger.e(TAG, "Remote service call security exception", se);
        } catch (IllegalArgumentException iae) {
            Logger.e(TAG, "Remote service call parameter exception", iae);
        } catch (NullPointerException npe) {
            Logger.e(TAG, "Remote service call null pointer exception", npe);
        } catch (Exception ex) {
            Logger.e(TAG, "Execute encryption/decryption operation unknown error", ex);
        }
        return false;
    }

    /**
     * Schedule EventBroker service binding with initial delay.
     */
    private void scheduleEventBrokerBinding() {
        Logger.d(TAG, "Scheduling EventBroker binding with " + EVENTBROKER_INITIAL_DELAY_MS + "ms delay");
        mRetryHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                bindEventBrokerService();
            }
        }, EVENTBROKER_INITIAL_DELAY_MS);
    }

    /**
     * Bind EventBroker service for sending error events.
     */
    private void bindEventBrokerService() {
        if (mEventBrokerBound) {
            Logger.d(TAG, "EventBroker already bound, skipping");
            return;
        }

        mEventBrokerRetryCount++;
        Logger.d(TAG, "Attempting to bind EventBroker service (attempt "
                + mEventBrokerRetryCount + "/" + MAX_EVENTBROKER_RETRIES + ")");

        try {
            Intent intent = new Intent("com.thundercomm.brokeragent.SERVICE");
            intent.setClassName("vendor.thundercomm.brokeragent", "vendor.thundercomm.brokeragent.BrokerAgentService");

            Logger.d(TAG, "EventBroker intent action: " + intent.getAction());
            Logger.d(TAG, "EventBroker intent className: " + intent.getComponent());

            mEventBrokerConnection = new ServiceConnection() {
                @Override
                public void onServiceConnected(ComponentName name, IBinder service) {
                    Logger.i(TAG, "EventBroker service connected successfully after "
                            + mEventBrokerRetryCount + " attempts");
                    Logger.d(TAG, "EventBroker component name: " + name);
                    mEventBroker = IEventBroker.Stub.asInterface(service);
                    mEventBrokerBound = true;
                    mIsRetryingEventBroker = false; // 重置重试状态
                    Logger.d(TAG, "EventBroker interface created: " + (mEventBroker != null));
                }

                @Override
                public void onServiceDisconnected(ComponentName name) {
                    Logger.w(TAG, "EventBroker service disconnected");
                    Logger.d(TAG, "EventBroker disconnected component: " + name);
                    mEventBroker = null;
                    mEventBrokerBound = false;
                }
            };

            boolean bindResult = bindService(intent, mEventBrokerConnection, Context.BIND_AUTO_CREATE);
            Logger.i(TAG, "EventBroker bind attempt result: " + bindResult);

            if (!bindResult) {
                Logger.e(TAG, "Failed to bind EventBroker service - bindService returned false");
                scheduleEventBrokerRetry();
            }

        } catch (Exception ex) {
            Logger.e(TAG, "Exception while binding EventBroker service", ex);
            scheduleEventBrokerRetry();
        }
    }

    /**
     * Schedule EventBroker service binding retry.
     */
    private void scheduleEventBrokerRetry() {
        if (mEventBrokerRetryCount >= MAX_EVENTBROKER_RETRIES) {
            Logger.w(TAG, "EventBroker binding failed after " + MAX_EVENTBROKER_RETRIES + " attempts, giving up");
            mIsRetryingEventBroker = false;
            return;
        }

        if (mIsRetryingEventBroker) {
            Logger.d(TAG, "EventBroker retry already scheduled");
            return;
        }

        mIsRetryingEventBroker = true;
        Logger.d(TAG, "Scheduling EventBroker retry " + mEventBrokerRetryCount
                + "/" + MAX_EVENTBROKER_RETRIES + " in " + EVENTBROKER_RETRY_DELAY_MS + "ms");

        mRetryHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                mIsRetryingEventBroker = false;
                bindEventBrokerService();
            }
        }, EVENTBROKER_RETRY_DELAY_MS);
    }

    /**
     * Send SD card not inserted error event using AIDL.
     * Format matches EventProvider's sendTestSdCardError exactly.
     */
    private void sendSdCardNotInsertedError() {
        if (!mEventBrokerBound || mEventBroker == null) {
            Logger.w(TAG, "EventBroker not available, cannot send SD card error");
            return;
        }

        try {
            Logger.d(TAG, "Sending SD card not inserted error via AIDL");

            // Create data object (matches EventProvider format exactly)
            JSONObject dataObject = new JSONObject();
            dataObject.put("error_type", "read_write_fail");  // 与EventProvider一致
            dataObject.put("test_trigger", false);  // 实际错误，不是测试
            dataObject.put("timestamp", System.currentTimeMillis());
            dataObject.put("source", "ConfigTool");  // 标识来源

            // Create main JSON object (matches EventProvider format exactly)
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 2);  // READ_WRITE_FAIL
            jsonObject.put("hwErrorCode", Constants.HW_ERROR_CODE_READ_WRITE_FAIL);  // HW_ERROR_CODE_READ_WRITE_FAIL
            jsonObject.put("data", dataObject);

            // Create event entry
            EventEntry eventEntry = new EventEntry();

            // Set header
            eventEntry.header = new EventHeader();
            eventEntry.header.categoryId = RUNTIME_ERROR_CATEGORY_ID; // RUNTIME_ERROR
            eventEntry.header.typeId = SD_CARD_HARDWARE_ID; // SD_CARD hardware ID
            eventEntry.header.timestamp = System.currentTimeMillis();
            eventEntry.header.lifetime = Constants.ONE_HOUR_IN_MILLISECONDS; // 1 hour in milliseconds

            // Set body (matches EventProvider format exactly)
            eventEntry.body = new EventBody();
            eventEntry.body.name = "sdcard_read_write_error";  // 与EventProvider一致
            eventEntry.body.payloadType = 0; // JSON payload type (0, not 1!)
            byte[] payloadBytes = jsonObject.toString().getBytes();  // 使用完整的jsonObject
            eventEntry.body.payloadData = payloadBytes;
            eventEntry.body.payloadSize = payloadBytes.length;

            // Publish event
            mEventBroker.publishEvent(eventEntry);

            Logger.i(TAG, "SD card error event sent successfully via AIDL");
            Logger.d(TAG, "Event format matches EventProvider exactly:");
            Logger.d(TAG, "  Event Name: sdcard_read_write_error");
            Logger.d(TAG, "  Category: RUNTIME_ERROR (" + RUNTIME_ERROR_CATEGORY_ID + ")");
            Logger.d(TAG, "  HW ID: SD_CARD (" + SD_CARD_HARDWARE_ID + ")");
            Logger.d(TAG, "  Error Code: READ_WRITE_FAIL (2)");
            Logger.d(TAG, "  HW Error Code: " + Constants.HW_ERROR_CODE_READ_WRITE_FAIL);
            Logger.d(TAG, "  PayloadType: JSON (0)");

        } catch (Exception ex) {
            Logger.e(TAG, "Failed to send SD card error via AIDL", ex);
        }
    }
}