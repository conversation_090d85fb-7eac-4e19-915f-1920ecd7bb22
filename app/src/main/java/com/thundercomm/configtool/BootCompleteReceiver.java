/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.thundercomm.configtool.utils.Logger;

/**
 * Boot complete broadcast receiver.
 */
public class BootCompleteReceiver extends BroadcastReceiver {
    private static final String TAG = "BootCompleteReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Logger.begin(TAG, "onReceive");

        String action = intent.getAction();
        Logger.d(TAG, "Received broadcast: " + action);

        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            Intent.ACTION_LOCKED_BOOT_COMPLETED.equals(action)) {
            Logger.i(TAG, "Device boot completed, preparing to start ConfigToolService");

            // Start ConfigToolService
            Intent serviceIntent = new Intent(context, ConfigToolService.class);
            context.startService(serviceIntent);

            Logger.i(TAG, "ConfigToolService has been started");
        }

        Logger.end(TAG, "onReceive", true);
    }
}