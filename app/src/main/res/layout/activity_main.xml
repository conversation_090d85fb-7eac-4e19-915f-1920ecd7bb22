<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
-->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="JSON Configuration Content:"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/json_input"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="8dp"
            android:background="@android:drawable/editbox_background"
            android:gravity="top|start"
            android:hint="Enter JSON configuration data"
            android:inputType="textMultiLine"
            android:padding="8dp"
            android:scrollbars="vertical" />

        <Button
            android:id="@+id/save_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Configuration" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:background="#CCCCCC" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Query Configuration Value:"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <EditText
                android:id="@+id/key_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Enter configuration key"
                android:padding="8dp"
                android:background="@android:drawable/editbox_background" />

            <Button
                android:id="@+id/get_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Get Value" />
        </LinearLayout>

        <Button
            android:id="@+id/generate_test_config_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Generate Test Config" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Quick Test:"
            android:textStyle="bold" />

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/test_configVersion_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="configVersion"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/test_eventRectime_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="eventRectime"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/test_recMode_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="recMode"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/test_appInfo_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="appInfo"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/test_fotaInfo_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="fotaTergetInfo"
                    android:textSize="12sp" />
            </LinearLayout>
        </HorizontalScrollView>

        <!-- 新功能测试按钮 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="新功能测试:"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical">

            <Button
                android:id="@+id/test_version_compare_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试版本比对功能"
                android:layout_marginBottom="4dp" />

            <Button
                android:id="@+id/test_encryption_retry_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试加密重试机制"
                android:layout_marginBottom="4dp" />

            <Button
                android:id="@+id/test_dynamic_sdcard_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试动态SD卡检测"
                android:layout_marginBottom="4dp" />

            <Button
                android:id="@+id/test_eventbroker_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试EventBroker错误发送"
                android:layout_marginBottom="4dp" />

            <Button
                android:id="@+id/test_external_version_check_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试外部版本检查"
                android:layout_marginBottom="8dp" />

        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="日志输出:"
            android:textStyle="bold" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="4dp"
            android:background="#f0f0f0">

            <ScrollView
                android:id="@+id/scroll_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/log_output"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="8dp"
                    android:textSize="14sp" />
            </ScrollView>
        </FrameLayout>

    </LinearLayout>
</ScrollView> 