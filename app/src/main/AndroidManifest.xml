<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.thundercomm.configtool">

    <!-- Read and write external storage permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <!-- For Android 11+, MANAGE_EXTERNAL_STORAGE permission is needed -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <!-- Boot auto-start permission -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- Query other packages visibility, resolving Android 11+ package visibility restrictions -->
    <queries>
        <package android:name="com.thundercomm.crown.fileencrypt" />
    </queries>

    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:directBootAware="true"
        android:persistent="true"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="30"
        >
        <activity
            android:name="com.thundercomm.configtool.MainActivity"
            android:exported="true">
        </activity>

        <!-- ConfigTool Service - Main functionality of the app -->
        <service
            android:name="com.thundercomm.configtool.ConfigToolService"
            android:enabled="true"
            android:exported="true"
            android:persistent="true">
            <intent-filter>
                <action android:name="com.thundercomm.configtool.ConfigToolService" />
            </intent-filter>
        </service>

        <!-- Boot complete receiver -->
        <receiver
            android:name=".BootCompleteReceiver"
            android:enabled="true"
            android:exported="true"
            android:priority="1000">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

    </application>

</manifest>